/* API Integration Styles */

/* API Status Indicator */
.api-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.api-status.enabled {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
}

.api-status.disabled {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

/* Current Provider Display */
.current-provider {
    font-weight: 600;
    color: #667eea;
    padding: 2px 6px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 6px;
}

/* API Config Button */
.api-config-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 2px solid #667eea;
    color: #ffffff;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.api-config-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.api-config-btn:hover::before {
    left: 100%;
}

.api-config-btn:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    border-color: #764ba2;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.api-config-btn.active {
    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
    border-color: #5a67d8;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
    animation: pulse-blue 2s infinite;
}

@keyframes pulse-blue {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* API Config Modal */
.api-config-modal {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.api-config-content {
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Provider Cards */
.provider-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.provider-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.provider-card.configured {
    border-color: #2ecc71;
}

.provider-card.current {
    border-color: #f39c12;
    background: rgba(243, 156, 18, 0.1);
}

/* Form Elements */
.api-form-group {
    margin-bottom: 20px;
}

.api-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: white;
}

.api-form-input {
    width: 100%;
    padding: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.api-form-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
}

.api-form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Select Dropdown */
.api-form-select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

/* Buttons */
.api-btn {
    padding: 12px 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 1rem;
}

.api-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.api-btn-primary {
    background: rgba(102, 126, 234, 0.8);
    color: white;
    border-color: #667eea;
}

.api-btn-success {
    background: rgba(46, 204, 113, 0.8);
    color: white;
    border-color: #2ecc71;
}

.api-btn-danger {
    background: rgba(231, 76, 60, 0.8);
    color: white;
    border-color: #e74c3c;
}

.api-btn-warning {
    background: rgba(243, 156, 18, 0.8);
    color: white;
    border-color: #f39c12;
}

.api-btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

/* Loading States */
.api-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #f39c12;
}

.api-loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error Messages */
.api-message {
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.api-message.success {
    background: rgba(46, 204, 113, 0.2);
    border: 1px solid rgba(46, 204, 113, 0.5);
    color: #2ecc71;
}

.api-message.error {
    background: rgba(231, 76, 60, 0.2);
    border: 1px solid rgba(231, 76, 60, 0.5);
    color: #e74c3c;
}

.api-message.warning {
    background: rgba(243, 156, 18, 0.2);
    border: 1px solid rgba(243, 156, 18, 0.5);
    color: #f39c12;
}

.api-message.info {
    background: rgba(52, 152, 219, 0.2);
    border: 1px solid rgba(52, 152, 219, 0.5);
    color: #3498db;
}

/* Provider Icons */
.provider-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.provider-icon.openai {
    color: #00a67e;
}

.provider-icon.gemini {
    color: #4285f4;
}

.provider-icon.claude {
    color: #ff6b35;
}

.provider-icon.cohere {
    color: #39c5bb;
}

.provider-icon.huggingface {
    color: #ff9d00;
}

.provider-icon.custom {
    color: #667eea;
}

.provider-icon.gemini-studio {
    color: #ffd700;
}

.provider-icon.perplexity {
    color: #20b2aa;
}

.provider-icon.groq {
    color: #ff6b35;
}

.provider-icon.mistral {
    color: #ff7f50;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.configured {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.status-badge.not-configured {
    background: rgba(243, 156, 18, 0.2);
    color: #f39c12;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.status-badge.current {
    background: rgba(102, 126, 234, 0.2);
    color: #667eea;
    border: 1px solid rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .api-config-content {
        width: 95%;
        padding: 20px;
        margin: 10px;
    }
    
    .provider-grid {
        grid-template-columns: 1fr;
    }
    
    .api-btn {
        width: 100%;
        justify-content: center;
        margin-bottom: 10px;
    }
    
    .api-config-footer {
        flex-direction: column;
        gap: 10px;
    }
}

/* Scrollbar Styling */
.api-config-content::-webkit-scrollbar {
    width: 8px;
}

.api-config-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.api-config-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.api-config-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Animation for provider cards */
.provider-card {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Test connection animation */
.test-connection {
    text-align: center;
    padding: 40px 20px;
}

.test-spinner {
    font-size: 3rem;
    color: #f39c12;
    animation: spin 1s linear infinite;
    margin: 20px 0;
}

/* Success checkmark animation */
.success-checkmark {
    font-size: 4rem;
    color: #2ecc71;
    animation: checkmarkBounce 0.6s ease-out;
}

@keyframes checkmarkBounce {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Error icon animation */
.error-icon {
    font-size: 4rem;
    color: #e74c3c;
    animation: errorShake 0.6s ease-out;
}

@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-10px);
    }
    75% {
        transform: translateX(10px);
    }
}

/* API Integration Toggle */
.api-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin: 10px 0;
}

.api-toggle-switch {
    position: relative;
    width: 50px;
    height: 25px;
    background: #ccc;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.api-toggle-switch.active {
    background: #2ecc71;
}

.api-toggle-switch::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 21px;
    height: 21px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.api-toggle-switch.active::before {
    transform: translateX(25px);
}

/* Tooltip */
.api-tooltip {
    position: relative;
    cursor: help;
}

.api-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.api-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}
