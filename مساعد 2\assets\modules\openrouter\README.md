# OpenRouter.ai Integration System
## نظام تكامل OpenRouter.ai مع المساعد التقني

### 📋 نظرة عامة

هذا النظام يوفر تكاملاً ذكياً مع خدمة OpenRouter.ai، مما يتيح للمساعد التقني الوصول إلى أكثر من 100 نموذج ذكاء اصطناعي مختلف بطريقة سلسة ومرنة.

### 🎯 الميزات الرئيسية

- **تكامل ذكي**: يتبديل تلقائياً بين OpenRouter والنظام المحلي
- **واجهة سهلة**: إعداد بسيط عبر واجهة مرئية
- **تخزين آمن**: حفظ الإعدادات محلياً مع التشفير
- **معالجة الأخطاء**: نظام شامل للتعامل مع الأخطاء
- **مرونة عالية**: دعم أوضاع تشغيل متعددة
- **تصميم معياري**: سهولة إضافة مزودين جدد

### 📁 هيكل الملفات

```
assets/modules/openrouter/
├── OpenRouterManager.js      # إدارة الاتصال والنماذج
├── OpenRouterInterface.js    # واجهة المستخدم
├── OpenRouterIntegration.js  # نظام التكامل الذكي
├── openrouter.css           # تنسيقات الواجهة
└── README.md               # هذا الملف
```

### 🚀 كيفية الاستخدام

#### 1. الإعداد الأولي
1. انقر على زر "OpenRouter.ai" في لوحة الإعدادات
2. أدخل مفتاح API من [OpenRouter.ai](https://openrouter.ai/keys)
3. انقر "الاتصال والمتابعة"

#### 2. اختيار النموذج
1. ستظهر قائمة بالنماذج المتاحة مصنفة حسب النوع
2. انقر على النموذج المطلوب
3. سيتم حفظ اختيارك تلقائياً

#### 3. الاستخدام
- المساعد سيستخدم OpenRouter تلقائياً عند توفره
- في حالة عدم التوفر، سيعود للنظام المحلي
- يمكن تغيير النموذج في أي وقت

### ⚙️ أوضاع التشغيل

#### 1. الوضع الذكي (Smart Mode)
- **الافتراضي**: يحاول استخدام OpenRouter أولاً
- **البديل**: يعود للنظام المحلي عند الفشل
- **الأمثل**: للاستخدام العادي

#### 2. الوضع الإجباري (Force Mode)
- **OpenRouter فقط**: لا يستخدم البديل المحلي
- **مفيد**: للاختبار أو الاستخدام المتخصص
- **تحذير**: قد يفشل إذا لم يكن OpenRouter متاحاً

#### 3. الوضع المعطل (Disabled Mode)
- **النظام المحلي فقط**: يتجاهل OpenRouter تماماً
- **مفيد**: لتوفير التكلفة أو عند مشاكل الشبكة

### 🔧 API المطور

#### OpenRouterManager
```javascript
// إعداد مفتاح API
window.openRouterManager.setApiKey('sk-or-v1-...');

// التحقق من الاتصال
await window.openRouterManager.validateConnection();

// إرسال رسالة
const response = await window.openRouterManager.sendMessage('مرحبا');

// اختيار نموذج
window.openRouterManager.setSelectedModel('anthropic/claude-3-sonnet');
```

#### OpenRouterIntegration
```javascript
// تغيير وضع التكامل
window.openRouterIntegration.setIntegrationMode('smart');

// اختبار التكامل
const test = await window.openRouterIntegration.testIntegration();

// الحصول على معلومات التكامل
const info = window.openRouterIntegration.getIntegrationInfo();
```

### 🛡️ الأمان والخصوصية

- **التشفير المحلي**: مفاتيح API محفوظة محلياً فقط
- **عدم التتبع**: لا يتم إرسال بيانات لخوادم خارجية غير OpenRouter
- **التحكم الكامل**: يمكن حذف البيانات في أي وقت

### 🔍 استكشاف الأخطاء

#### مشاكل الاتصال
```javascript
// فحص حالة الاتصال
console.log(window.openRouterManager.getConnectionStatus());

// اختبار الاتصال
try {
    await window.openRouterManager.validateConnection();
    console.log('الاتصال يعمل بشكل صحيح');
} catch (error) {
    console.error('خطأ في الاتصال:', error.message);
}
```

#### مشاكل النماذج
```javascript
// عرض النماذج المتاحة
console.log(window.openRouterManager.availableModels);

// فحص النموذج المختار
console.log(window.openRouterManager.getSelectedModelInfo());
```

### 📊 مراقبة الأداء

```javascript
// إحصائيات الاستخدام
const stats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0
};

// مراقبة الطلبات
window.openRouterManager.on('request', (data) => {
    stats.totalRequests++;
    console.log('طلب جديد:', data);
});
```

### 🔮 التطوير المستقبلي

#### مزودون مخططون
- **Together.ai**: نماذج مفتوحة المصدر
- **Groq**: معالجة فائقة السرعة
- **Replicate**: نماذج متخصصة
- **Hugging Face**: مجتمع المطورين

#### ميزات مخططة
- **تخزين السحابي**: مزامنة الإعدادات
- **إحصائيات متقدمة**: تتبع الاستخدام والتكلفة
- **نماذج مخصصة**: دعم النماذج المدربة محلياً
- **واجهة API**: للتطبيقات الخارجية

### 📞 الدعم والمساعدة

#### الأخطاء الشائعة

1. **"مفتاح API غير صالح"**
   - تأكد من صحة المفتاح
   - تحقق من صلاحية المفتاح في OpenRouter

2. **"فشل في تحميل النماذج"**
   - تحقق من اتصال الإنترنت
   - تأكد من عدم انتهاء صلاحية المفتاح

3. **"رصيد غير كافي"**
   - أضف رصيد لحسابك في OpenRouter
   - تحقق من حدود الاستخدام

#### التواصل
- **GitHub Issues**: للمشاكل التقنية
- **Discord**: للدعم المباشر
- **Email**: للاستفسارات العامة

### 📄 الترخيص

هذا النظام مرخص تحت رخصة MIT. يمكن استخدامه وتعديله بحرية مع الحفاظ على حقوق المؤلف.

---

**تم التطوير بواسطة**: فريق المساعد التقني  
**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0
