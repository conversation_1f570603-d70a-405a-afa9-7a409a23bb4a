/**
 * Bug Bounty Report Exporter v4.0
 * مُصدر التقارير الاحترافية مع دعم الصور الحقيقية والاستغلال الفعلي
 * متوافق مع Bug Bounty System v4.0
 */

class BugBountyReportExporter {
    constructor() {
        this.version = '4.0';
        this.systemName = 'Bug Bounty Report Exporter v4.0';
        this.reportData = null;
        this.targetUrl = null;
        this.visualizations = [];
        this.screenshots = [];
        this.exploitationResults = [];
        this.realTestingData = [];

        console.log(`✅ ${this.systemName} تم تحميله بنجاح`);
        console.log('📊 ميزات v4.0: تصدير الصور + نتائج الاستغلال + البيانات الحقيقية');
    }

    // إنشاء تقرير PDF
    async generatePDFReport(reportContent, targetUrl) {
        try {
            // استخدام jsPDF لإنشاء PDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // إعداد الخط العربي
            doc.setFont('helvetica');
            doc.setFontSize(16);
            
            // العنوان
            doc.text('Bug Bounty Security Report', 20, 20);
            doc.setFontSize(12);
            doc.text(`Target: ${targetUrl}`, 20, 35);
            doc.text(`Date: ${new Date().toLocaleString()}`, 20, 45);
            
            // المحتوى
            const lines = this.formatContentForPDF(reportContent);
            let yPosition = 60;
            
            lines.forEach(line => {
                if (yPosition > 270) {
                    doc.addPage();
                    yPosition = 20;
                }
                doc.text(line, 20, yPosition);
                yPosition += 7;
            });
            
            // حفظ الملف
            const fileName = `bug_bounty_report_${this.sanitizeFileName(targetUrl)}_${Date.now()}.pdf`;
            doc.save(fileName);
            
            return fileName;
        } catch (error) {
            console.error('خطأ في إنشاء PDF:', error);
            return this.generateTextReport(reportContent, targetUrl);
        }
    }

    // إنشاء تقرير نصي
    generateTextReport(reportContent, targetUrl) {
        const reportText = `
BUG BOUNTY SECURITY REPORT
==========================

Target: ${targetUrl}
Date: ${new Date().toLocaleString()}
Generated by: Advanced Bug Bounty System

${this.cleanTextContent(reportContent)}

---
Report generated by Bug Bounty Core v2.0
Professional Security Analysis System
        `.trim();

        // إنشاء ملف للتحميل
        const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        
        const fileName = `bug_bounty_report_${this.sanitizeFileName(targetUrl)}_${Date.now()}.txt`;
        
        // إنشاء رابط التحميل
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = fileName;
        downloadLink.style.display = 'none';
        
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        
        // تنظيف الذاكرة
        setTimeout(() => URL.revokeObjectURL(url), 1000);
        
        return fileName;
    }

    // إنشاء تقرير JSON
    generateJSONReport(reportContent, analysisData, targetUrl) {
        const jsonReport = {
            metadata: {
                target: targetUrl,
                timestamp: new Date().toISOString(),
                generator: 'Bug Bounty Core v2.0',
                report_type: 'comprehensive_security_analysis'
            },
            analysis_data: analysisData,
            security_report: reportContent,
            summary: this.extractSummary(reportContent),
            vulnerabilities: this.extractVulnerabilities(reportContent)
        };

        const blob = new Blob([JSON.stringify(jsonReport, null, 2)], { 
            type: 'application/json;charset=utf-8' 
        });
        const url = URL.createObjectURL(blob);
        
        const fileName = `bug_bounty_analysis_${this.sanitizeFileName(targetUrl)}_${Date.now()}.json`;
        
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = fileName;
        downloadLink.style.display = 'none';
        
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        
        setTimeout(() => URL.revokeObjectURL(url), 1000);
        
        return fileName;
    }

    // تنظيف اسم الملف
    sanitizeFileName(url) {
        return url.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 50);
    }

    // تنسيق المحتوى للـ PDF
    formatContentForPDF(content) {
        // إزالة HTML tags وتنسيق النص
        const cleanContent = content.replace(/<[^>]*>/g, '');
        const lines = cleanContent.split('\n');
        const formattedLines = [];
        
        lines.forEach(line => {
            const trimmedLine = line.trim();
            if (trimmedLine) {
                // تقسيم الأسطر الطويلة
                if (trimmedLine.length > 80) {
                    const words = trimmedLine.split(' ');
                    let currentLine = '';
                    
                    words.forEach(word => {
                        if ((currentLine + word).length > 80) {
                            if (currentLine) {
                                formattedLines.push(currentLine.trim());
                                currentLine = word + ' ';
                            } else {
                                formattedLines.push(word);
                            }
                        } else {
                            currentLine += word + ' ';
                        }
                    });
                    
                    if (currentLine.trim()) {
                        formattedLines.push(currentLine.trim());
                    }
                } else {
                    formattedLines.push(trimmedLine);
                }
            }
        });
        
        return formattedLines;
    }

    // تنظيف المحتوى النصي
    cleanTextContent(content) {
        return content
            .replace(/<[^>]*>/g, '')
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/\s+/g, ' ')
            .trim();
    }

    // استخراج الملخص
    extractSummary(content) {
        const summaryMatch = content.match(/ملخص التقييم(.*?)الثغرات المكتشفة/s);
        return summaryMatch ? this.cleanTextContent(summaryMatch[1]) : 'ملخص غير متاح';
    }

    // استخراج الثغرات
    extractVulnerabilities(content) {
        const vulnerabilities = [];
        const vulnPattern = /#### (\d+)\. ([^#]+?)(?=####|\n### |$)/gs;
        let match;
        
        while ((match = vulnPattern.exec(content)) !== null) {
            const vulnText = match[2];
            const vulnerability = {
                id: match[1],
                name: this.extractField(vulnText, 'اسم الثغرة'),
                type: this.extractField(vulnText, 'النوع'),
                location: this.extractField(vulnText, 'الموقع'),
                severity: this.extractField(vulnText, 'الخطورة'),
                cvss: this.extractField(vulnText, 'CVSS Score'),
                description: this.extractField(vulnText, 'الوصف'),
                impact: this.extractField(vulnText, 'التأثير'),
                remediation: this.extractField(vulnText, 'الإصلاح')
            };
            vulnerabilities.push(vulnerability);
        }
        
        return vulnerabilities;
    }

    // استخراج حقل معين
    extractField(text, fieldName) {
        const pattern = new RegExp(`\\*\\*${fieldName}:\\*\\*\\s*([^\\n*]+)`, 'i');
        const match = text.match(pattern);
        return match ? match[1].trim() : 'غير محدد';
    }

    // إنشاء حاوي التحميل الاحترافي
    createDownloadContainer(reportContent, analysisData, targetUrl) {
        const container = document.createElement('div');
        container.className = 'bug-bounty-download-container';
        container.innerHTML = `
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 20px 0; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                <div style="text-align: center; margin-bottom: 20px;">
                    <h3 style="margin: 0; color: #fff; font-size: 1.5em;">📄 تقرير Bug Bounty جاهز</h3>
                    <p style="margin: 10px 0; opacity: 0.9;">تقرير فحص أمني شامل للموقع: ${targetUrl}</p>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <button onclick="window.bugBountyExporter.downloadPDF('${targetUrl}')" 
                            style="background: #e74c3c; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-size: 1em; transition: all 0.3s; box-shadow: 0 4px 15px rgba(231,76,60,0.3);">
                        📄 تحميل PDF
                    </button>
                    
                    <button onclick="window.bugBountyExporter.downloadText('${targetUrl}')" 
                            style="background: #27ae60; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-size: 1em; transition: all 0.3s; box-shadow: 0 4px 15px rgba(39,174,96,0.3);">
                        📝 تحميل نصي
                    </button>
                    
                    <button onclick="window.bugBountyExporter.downloadJSON('${targetUrl}')" 
                            style="background: #3498db; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-size: 1em; transition: all 0.3s; box-shadow: 0 4px 15px rgba(52,152,219,0.3);">
                        📊 تحميل JSON
                    </button>
                    
                    <button onclick="navigator.clipboard.writeText(window.bugBountyExporter.getCleanReport())" 
                            style="background: #9b59b6; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-size: 1em; transition: all 0.3s; box-shadow: 0 4px 15px rgba(155,89,182,0.3);">
                        📋 نسخ التقرير
                    </button>
                </div>
                
                <div style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <p style="margin: 0; font-size: 0.9em; opacity: 0.8;">
                        📅 تم إنشاء التقرير: ${new Date().toLocaleString('ar-SA')}
                    </p>
                </div>
            </div>
        `;
        
        // حفظ البيانات للاستخدام لاحقاً
        this.reportData = reportContent;
        this.analysisData = analysisData;
        this.targetUrl = targetUrl;
        
        return container;
    }

    // دوال التحميل للأزرار
    downloadPDF(targetUrl) {
        if (this.reportData) {
            this.generatePDFReport(this.reportData, targetUrl);
        }
    }

    downloadText(targetUrl) {
        if (this.reportData) {
            this.generateTextReport(this.reportData, targetUrl);
        }
    }

    downloadJSON(targetUrl) {
        if (this.reportData && this.analysisData) {
            this.generateJSONReport(this.reportData, this.analysisData, targetUrl);
        }
    }

    getCleanReport() {
        return this.reportData ? this.cleanTextContent(this.reportData) : '';
    }
}

// إنشاء مثيل عام
window.bugBountyExporter = new BugBountyReportExporter();

// تصدير الكلاس
window.BugBountyReportExporter = BugBountyReportExporter;
