// واجهة إعدادات Hugging Face
class HuggingFaceSettings {
    constructor() {
        this.isVisible = false;
        this.container = null;
        console.log('🎛️ تم تهيئة واجهة إعدادات Hugging Face');
    }

    // إظهار واجهة الإعدادات
    show() {
        if (this.isVisible) return;

        this.createSettingsInterface();
        this.isVisible = true;
        console.log('👁️ تم إظهار إعدادات Hugging Face');
    }

    // إخفاء واجهة الإعدادات
    hide() {
        if (this.container) {
            this.container.remove();
            this.container = null;
        }
        this.isVisible = false;
        console.log('🙈 تم إخفاء إعدادات Hugging Face');
    }

    // إنشاء واجهة الإعدادات
    createSettingsInterface() {
        // إنشاء الحاوية الرئيسية
        this.container = document.createElement('div');
        this.container.className = 'hf-settings-overlay';
        this.container.innerHTML = this.getSettingsHTML();

        // إضافة الأنماط
        this.addStyles();

        // إضافة للصفحة
        document.body.appendChild(this.container);

        // ربط الأحداث
        this.bindEvents();

        // تحديث القيم الحالية
        this.updateCurrentValues();
    }

    // توليد خيارات النماذج مع التصنيف
    generateModelOptions(hfManager) {
        const supportedModels = hfManager.getSupportedModels();
        const customModels = hfManager.getCustomModels();
        const categories = hfManager.getAvailableCategories();

        let optionsHTML = '';

        // النماذج المدعومة مصنفة
        categories.forEach(category => {
            if (category === 'custom') return; // سنتعامل مع المخصصة منفصلة

            const categoryModels = hfManager.getModelsByCategory(category);
            if (Object.keys(categoryModels).length > 0) {
                const categoryName = this.getCategoryDisplayName(category);
                optionsHTML += `<optgroup label="${categoryName}">`;

                Object.entries(categoryModels).forEach(([id, info]) => {
                    optionsHTML += `<option value="${id}">${info.name} - ${info.description}</option>`;
                });

                optionsHTML += '</optgroup>';
            }
        });

        // النماذج المخصصة
        if (Object.keys(customModels).length > 0) {
            optionsHTML += '<optgroup label="النماذج المخصصة">';
            Object.entries(customModels).forEach(([id, info]) => {
                optionsHTML += `<option value="${id}">${info.name} - ${info.description} [مخصص]</option>`;
            });
            optionsHTML += '</optgroup>';
        }

        return optionsHTML;
    }

    // الحصول على اسم الفئة للعرض
    getCategoryDisplayName(category) {
        const categoryNames = {
            'advanced': '🚀 النماذج المتقدمة',
            'conversation': '💬 نماذج المحادثة',
            'multilingual': '🌍 النماذج متعددة اللغات',
            'coding': '💻 نماذج البرمجة',
            'specialized': '🎯 النماذج المتخصصة',
            'translation': '🔄 نماذج الترجمة',
            'custom': '⭐ النماذج المخصصة'
        };
        return categoryNames[category] || category;
    }

    // HTML الواجهة
    getSettingsHTML() {
        const hfManager = window.huggingFaceManager;
        const status = hfManager.getStatus();
        const models = hfManager.getSupportedModels();

        return `
            <div class="hf-settings-modal">
                <div class="hf-settings-header">
                    <h2>🤗 إعدادات Hugging Face</h2>
                    <button class="hf-close-btn" id="hf-close-settings">×</button>
                </div>

                <div class="hf-settings-content">
                    <!-- حالة الخدمة -->
                    <div class="hf-section">
                        <h3>📊 حالة الخدمة</h3>
                        <div class="hf-status-card">
                            <div class="status-item">
                                <span>الحالة:</span>
                                <span id="hf-status" class="status ${status.isEnabled ? 'enabled' : 'disabled'}">
                                    ${status.isEnabled ? 'مفعل' : 'معطل'}
                                </span>
                            </div>
                            <div class="status-item">
                                <span>التوكن:</span>
                                <span class="status ${status.hasToken ? 'enabled' : 'disabled'}">
                                    ${status.hasToken ? 'محفوظ' : 'غير محفوظ'}
                                </span>
                            </div>
                            <div class="status-item">
                                <span>النموذج:</span>
                                <span class="status ${status.hasModel ? 'enabled' : 'disabled'}">
                                    ${status.hasModel ? status.modelInfo?.name || 'محدد' : 'غير محدد'}
                                </span>
                            </div>
                        </div>
                        <button id="hf-toggle-btn" class="btn ${status.isEnabled ? 'btn-danger' : 'btn-success'}">
                            ${status.isEnabled ? 'إلغاء التفعيل' : 'تفعيل'}
                        </button>
                    </div>

                    <!-- إعدادات الاتصال -->
                    <div class="hf-section">
                        <h3>🔑 إعدادات الاتصال</h3>
                        <div class="hf-form-group">
                            <label for="hf-api-token">توكن API:</label>
                            <div class="hf-input-group">
                                <input type="password" id="hf-api-token" placeholder="hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
                                <button type="button" id="hf-toggle-token-visibility" class="btn-icon">👁️</button>
                            </div>
                            <small>احصل على التوكن من <a href="https://huggingface.co/settings/tokens" target="_blank">Hugging Face</a></small>
                        </div>

                        <div class="hf-form-group">
                            <label class="hf-checkbox">
                                <input type="checkbox" id="hf-use-custom-url">
                                <span class="checkmark"></span>
                                استخدام API URL مخصص
                            </label>
                            <small>مفيد للنماذج التي تحتاج URL خاص أو للخوادم المحلية</small>
                        </div>

                        <div class="hf-form-group" id="hf-custom-url-section" style="display: none;">
                            <label for="hf-custom-api-url">API URL المخصص:</label>
                            <input type="text" id="hf-custom-api-url" placeholder="https://api-inference.huggingface.co/models/username/model-name">
                            <small>أدخل URL كامل للنموذج أو الخادم المخصص</small>

                            <div class="url-examples">
                                <strong>أمثلة URLs صحيحة:</strong>
                                <div class="example-urls">
                                    <button type="button" class="url-example" data-url="https://api-inference.huggingface.co/models/gpt2">
                                        GPT-2 (مؤكد)
                                    </button>
                                    <button type="button" class="url-example" data-url="https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium">
                                        DialoGPT Medium
                                    </button>
                                    <button type="button" class="url-example" data-url="https://api-inference.huggingface.co/models/facebook/bart-large-cnn">
                                        BART CNN
                                    </button>
                                    <button type="button" class="url-example" data-url="https://api-inference.huggingface.co/models/DeepMount00/GPT-4o-ITA-INSTRUCT">
                                        GPT-4o ITA
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="hf-form-group">
                            <label for="hf-model-select">اختيار النموذج:</label>
                            <div class="model-selection-section">
                                <div class="model-fetch-controls">
                                    <button type="button" id="hf-fetch-models" class="btn btn-primary">
                                        <i class="fas fa-download"></i> جلب النماذج المتاحة
                                    </button>
                                    <button type="button" id="hf-refresh-models" class="btn btn-secondary" style="display: none;">
                                        <i class="fas fa-sync"></i> تحديث
                                    </button>
                                </div>
                                <div id="hf-fetch-progress" style="display: none;">
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="hf-progress-fill"></div>
                                    </div>
                                    <div class="progress-text" id="hf-progress-text">جاري الفحص...</div>
                                </div>
                                <select id="hf-model-select">
                                    <option value="">-- أدخل التوكن واضغط "جلب النماذج المتاحة" --</option>
                                    ${this.generateModelOptions(hfManager)}
                                </select>
                            </div>
                        </div>

                        <div class="hf-form-group">
                            <label>إضافة نموذج مخصص:</label>
                            <div class="custom-model-section">
                                <input type="text" id="hf-custom-model-id" placeholder="مثال: username/model-name">
                                <input type="text" id="hf-custom-model-name" placeholder="اسم النموذج">
                                <input type="text" id="hf-custom-model-desc" placeholder="وصف النموذج (اختياري)">
                                <select id="hf-custom-model-type">
                                    <option value="text-generation">توليد نص</option>
                                    <option value="conversational">محادثة</option>
                                    <option value="code-generation">توليد كود</option>
                                    <option value="translation">ترجمة</option>
                                    <option value="summarization">تلخيص</option>
                                    <option value="embeddings">تضمين</option>
                                </select>
                                <button type="button" id="hf-add-custom-model" class="btn btn-primary">إضافة النموذج</button>
                            </div>
                        </div>

                        <div id="hf-current-model-info"></div>
                    </div>

                    <!-- إعدادات النموذج -->
                    <div class="hf-section">
                        <h3>⚙️ إعدادات النموذج</h3>
                        <div class="hf-settings-grid">
                            <div class="hf-form-group">
                                <label for="hf-max-tokens">الحد الأقصى للرموز:</label>
                                <input type="range" id="hf-max-tokens" min="50" max="1000" value="500">
                                <span id="hf-max-tokens-value">500</span>
                            </div>

                            <div class="hf-form-group">
                                <label for="hf-temperature">درجة الحرارة:</label>
                                <input type="range" id="hf-temperature" min="0.1" max="2.0" step="0.1" value="0.7">
                                <span id="hf-temperature-value">0.7</span>
                            </div>

                            <div class="hf-form-group">
                                <label for="hf-top-p">Top P:</label>
                                <input type="range" id="hf-top-p" min="0.1" max="1.0" step="0.1" value="0.9">
                                <span id="hf-top-p-value">0.9</span>
                            </div>

                            <div class="hf-form-group">
                                <label for="hf-repetition-penalty">عقوبة التكرار:</label>
                                <input type="range" id="hf-repetition-penalty" min="1.0" max="2.0" step="0.1" value="1.1">
                                <span id="hf-repetition-penalty-value">1.1</span>
                            </div>
                        </div>

                        <div class="hf-checkbox-group">
                            <label class="hf-checkbox">
                                <input type="checkbox" id="hf-wait-for-model" checked>
                                <span class="checkmark"></span>
                                انتظار تحميل النموذج
                            </label>
                            <label class="hf-checkbox">
                                <input type="checkbox" id="hf-use-cache" checked>
                                <span class="checkmark"></span>
                                استخدام التخزين المؤقت
                            </label>
                            <label class="hf-checkbox">
                                <input type="checkbox" id="hf-auto-detect-method" checked>
                                <span class="checkmark"></span>
                                كشف تلقائي لطريقة HTTP (يحل مشكلة 405)
                            </label>
                        </div>
                    </div>

                    <!-- دليل النماذج -->
                    <div class="hf-section">
                        <h3>📚 دليل الاستخدام</h3>
                        <div class="hf-guide">
                            <div class="guide-item">
                                <strong>✅ النماذج المؤكدة:</strong>
                                <p>هذه النماذج تم اختبارها وتعمل بشكل مؤكد مع Inference API</p>
                            </div>
                            <div class="guide-item">
                                <strong>🧪 النماذج التجريبية:</strong>
                                <p>قد تعمل أو لا تعمل - جربها واختبرها</p>
                            </div>
                            <div class="guide-item">
                                <strong>🔗 API URL مخصص:</strong>
                                <p>استخدم هذا إذا كان النموذج يحتاج URL خاص أو إذا كنت تستخدم خادم محلي</p>
                                <p><strong>مثال:</strong> https://api-inference.huggingface.co/models/username/model-name</p>
                            </div>
                            <div class="guide-item">
                                <strong>⚠️ حل مشاكل الأخطاء:</strong>
                                <p><strong>HTTP 404:</strong> النموذج غير موجود - جرب URL مخصص<br>
                                <strong>HTTP 405:</strong> طريقة غير مدعومة - فعل "كشف تلقائي لطريقة HTTP"<br>
                                <strong>HTTP 401:</strong> توكن خاطئ - تحقق من التوكن<br>
                                <strong>HTTP 403:</strong> ممنوع - النموذج خاص أو يحتاج اشتراك<br>
                                <strong>model_kwargs:</strong> معاملات غير مدعومة - النظام سيعيد المحاولة تلقائ<|im_start|></p>
                            </div>
                            <div class="guide-item">
                                <strong>🌐 جلب النماذج المتاحة:</strong>
                                <p>• أدخل التوكن أولاً<br>
                                • اضغط "جلب النماذج المتاحة"<br>
                                • انتظر فحص النماذج (قد يستغرق دقيقة)<br>
                                • اختر من النماذج المؤكدة (🟢)</p>
                            </div>
                            <div class="guide-item">
                                <strong>💡 نصائح:</strong>
                                <p>• النماذج المؤكدة (🟢) تعمل 100%<br>
                                • استخدم "تحديث" لإعادة فحص النماذج<br>
                                • فعل الكشف التلقائي لطريقة HTTP<br>
                                • تأكد من صحة التوكن والصلاحيات</p>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="hf-section">
                        <h3>🧪 اختبار الاتصال</h3>
                        <div class="hf-test-section">
                            <button id="hf-test-connection" class="btn btn-primary">اختبار الاتصال</button>
                            <div id="hf-test-result"></div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="hf-actions">
                        <button id="hf-save-settings" class="btn btn-success">💾 حفظ الإعدادات</button>
                        <button id="hf-reset-settings" class="btn btn-warning">🔄 إعادة تعيين</button>
                        <button id="hf-close-modal" class="btn btn-secondary">إغلاق</button>
                    </div>
                </div>
            </div>
        `;
    }

    // إضافة الأنماط
    addStyles() {
        if (document.getElementById('hf-settings-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'hf-settings-styles';
        styles.textContent = `
            .hf-settings-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            }

            .hf-settings-modal {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                width: 90%;
                max-width: 800px;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                color: white;
            }

            .hf-settings-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px 30px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }

            .hf-settings-header h2 {
                margin: 0;
                font-size: 1.5em;
            }

            .hf-close-btn {
                background: none;
                border: none;
                color: white;
                font-size: 2em;
                cursor: pointer;
                padding: 0;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                transition: background 0.3s;
            }

            .hf-close-btn:hover {
                background: rgba(255, 255, 255, 0.2);
            }

            .hf-settings-content {
                padding: 20px 30px;
            }

            .hf-section {
                margin-bottom: 30px;
                padding: 20px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                backdrop-filter: blur(10px);
            }

            .hf-section h3 {
                margin: 0 0 15px 0;
                color: #fff;
                font-size: 1.2em;
            }

            .hf-status-card {
                background: rgba(255, 255, 255, 0.1);
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 15px;
            }

            .status-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
            }

            .status.enabled {
                color: #4CAF50;
                font-weight: bold;
            }

            .status.disabled {
                color: #f44336;
                font-weight: bold;
            }

            .hf-form-group {
                margin-bottom: 20px;
            }

            .hf-form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: bold;
                color: #fff;
            }

            .hf-input-group {
                display: flex;
                gap: 10px;
            }

            .hf-form-group input,
            .hf-form-group select {
                width: 100%;
                padding: 12px;
                border: none;
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.9);
                color: #333;
                font-size: 14px;
            }

            .hf-form-group input[type="range"] {
                background: transparent;
            }

            .hf-settings-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }

            .hf-checkbox-group {
                display: flex;
                gap: 20px;
                flex-wrap: wrap;
            }

            .hf-checkbox {
                display: flex;
                align-items: center;
                cursor: pointer;
                color: white;
            }

            .hf-checkbox input {
                margin-right: 10px;
            }

            .hf-test-section {
                text-align: center;
            }

            #hf-test-result {
                margin-top: 15px;
                padding: 10px;
                border-radius: 8px;
                min-height: 20px;
            }

            .hf-test-result.success {
                background: rgba(76, 175, 80, 0.2);
                color: #4CAF50;
                border: 1px solid #4CAF50;
            }

            .hf-test-result.error {
                background: rgba(244, 67, 54, 0.2);
                color: #f44336;
                border: 1px solid #f44336;
            }

            .hf-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
                flex-wrap: wrap;
                margin-top: 30px;
            }

            .btn {
                padding: 12px 24px;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: bold;
                transition: all 0.3s;
                text-decoration: none;
                display: inline-block;
            }

            .btn-success {
                background: #4CAF50;
                color: white;
            }

            .btn-danger {
                background: #f44336;
                color: white;
            }

            .btn-primary {
                background: #2196F3;
                color: white;
            }

            .btn-warning {
                background: #FF9800;
                color: white;
            }

            .btn-secondary {
                background: #607D8B;
                color: white;
            }

            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            }

            .btn-icon {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                padding: 8px;
                border-radius: 5px;
                cursor: pointer;
            }

            .current-model-info {
                background: rgba(255, 255, 255, 0.1);
                padding: 15px;
                border-radius: 10px;
                margin-top: 15px;
            }

            .model-type {
                background: rgba(255, 255, 255, 0.2);
                padding: 4px 8px;
                border-radius: 15px;
                font-size: 0.8em;
                display: inline-block;
                margin-top: 5px;
            }

            small {
                color: rgba(255, 255, 255, 0.8);
                font-size: 0.9em;
            }

            small a {
                color: #81C784;
                text-decoration: none;
            }

            small a:hover {
                text-decoration: underline;
            }

            /* أنماط النماذج المخصصة */
            .custom-model-section {
                background: rgba(255, 255, 255, 0.1);
                padding: 15px;
                border-radius: 10px;
                margin-top: 10px;
                display: grid;
                gap: 10px;
            }

            .custom-model-section input,
            .custom-model-section select {
                width: 100%;
                padding: 10px;
                border: none;
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.9);
                color: #333;
                font-size: 14px;
            }

            .custom-model-section input::placeholder {
                color: #666;
                font-style: italic;
            }

            /* أنماط الرسائل */
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }

            .hf-message-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .hf-message-content i {
                font-size: 1.2em;
            }

            /* تحسين optgroup */
            optgroup {
                font-weight: bold;
                color: #333;
                background: #f0f0f0;
            }

            optgroup option {
                font-weight: normal;
                color: #666;
                background: white;
                padding-left: 20px;
            }

            /* أنماط الدليل */
            .hf-guide {
                background: rgba(255, 255, 255, 0.1);
                padding: 15px;
                border-radius: 10px;
                margin-top: 10px;
            }

            .guide-item {
                margin-bottom: 15px;
                padding: 10px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                border-left: 4px solid #4CAF50;
            }

            .guide-item:last-child {
                margin-bottom: 0;
            }

            .guide-item strong {
                display: block;
                margin-bottom: 5px;
                color: #fff;
            }

            .guide-item p {
                margin: 0;
                color: rgba(255, 255, 255, 0.9);
                font-size: 0.9em;
                line-height: 1.4;
            }

            /* أنماط أمثلة URLs */
            .url-examples {
                margin-top: 10px;
                padding: 10px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
            }

            .url-examples strong {
                display: block;
                margin-bottom: 10px;
                color: #fff;
                font-size: 0.9em;
            }

            .example-urls {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 8px;
            }

            .url-example {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.8em;
                transition: all 0.3s ease;
                text-align: left;
            }

            .url-example:hover {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.4);
                transform: translateY(-1px);
            }

            .url-example:active {
                transform: translateY(0);
                background: rgba(255, 255, 255, 0.3);
            }

            /* أنماط جلب النماذج */
            .model-selection-section {
                background: rgba(255, 255, 255, 0.05);
                padding: 15px;
                border-radius: 10px;
                margin-top: 10px;
            }

            .model-fetch-controls {
                display: flex;
                gap: 10px;
                margin-bottom: 15px;
            }

            .model-fetch-controls .btn {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 10px 15px;
                font-size: 0.9em;
            }

            .model-fetch-controls .btn i {
                font-size: 1em;
            }

            /* شريط التقدم */
            .progress-bar {
                width: 100%;
                height: 20px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                overflow: hidden;
                margin-bottom: 10px;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #4CAF50, #81C784);
                width: 0%;
                transition: width 0.3s ease;
                border-radius: 10px;
            }

            .progress-text {
                text-align: center;
                color: rgba(255, 255, 255, 0.9);
                font-size: 0.9em;
                margin-bottom: 15px;
            }

            /* تحسين قائمة النماذج */
            #hf-model-select {
                min-height: 120px;
                max-height: 200px;
                overflow-y: auto;
            }

            #hf-model-select optgroup {
                background: rgba(255, 255, 255, 0.1);
                color: #fff;
                font-weight: bold;
                padding: 5px;
            }

            #hf-model-select option {
                background: rgba(255, 255, 255, 0.9);
                color: #333;
                padding: 8px;
            }

            #hf-model-select option:hover {
                background: rgba(255, 255, 255, 1);
            }

            /* حالات مختلفة للأزرار */
            .btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none !important;
            }

            .btn.loading {
                position: relative;
                color: transparent;
            }

            .btn.loading::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 16px;
                height: 16px;
                margin: -8px 0 0 -8px;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                color: white;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;

        document.head.appendChild(styles);
    }

    // ربط الأحداث
    bindEvents() {
        const hfManager = window.huggingFaceManager;

        // إغلاق النافذة
        document.getElementById('hf-close-settings').onclick = () => this.hide();
        document.getElementById('hf-close-modal').onclick = () => this.hide();

        // النقر خارج النافذة
        this.container.onclick = (e) => {
            if (e.target === this.container) this.hide();
        };

        // تفعيل/إلغاء تفعيل
        document.getElementById('hf-toggle-btn').onclick = () => {
            hfManager.toggle();
        };

        // إظهار/إخفاء التوكن
        document.getElementById('hf-toggle-token-visibility').onclick = () => {
            const tokenInput = document.getElementById('hf-api-token');
            const btn = document.getElementById('hf-toggle-token-visibility');
            if (tokenInput.type === 'password') {
                tokenInput.type = 'text';
                btn.textContent = '🙈';
            } else {
                tokenInput.type = 'password';
                btn.textContent = '👁️';
            }
        };

        // تحديث قيم المنزلقات
        this.bindRangeInputs();

        // حفظ الإعدادات
        document.getElementById('hf-save-settings').onclick = () => this.saveSettings();

        // إعادة تعيين
        document.getElementById('hf-reset-settings').onclick = () => this.resetSettings();

        // اختبار الاتصال
        document.getElementById('hf-test-connection').onclick = () => this.testConnection();

        // تغيير النموذج
        document.getElementById('hf-model-select').onchange = (e) => {
            hfManager.setModel(e.target.value);
        };

        // إضافة نموذج مخصص
        document.getElementById('hf-add-custom-model').onclick = () => {
            this.addCustomModel();
        };

        // تفعيل/إلغاء تفعيل URL مخصص
        document.getElementById('hf-use-custom-url').onchange = (e) => {
            const customUrlSection = document.getElementById('hf-custom-url-section');
            if (e.target.checked) {
                customUrlSection.style.display = 'block';
            } else {
                customUrlSection.style.display = 'none';
            }
        };

        // أحداث أمثلة URLs
        document.querySelectorAll('.url-example').forEach(button => {
            button.onclick = () => {
                const url = button.getAttribute('data-url');
                document.getElementById('hf-custom-api-url').value = url;
                document.getElementById('hf-use-custom-url').checked = true;
                document.getElementById('hf-custom-url-section').style.display = 'block';

                // إظهار رسالة تأكيد
                this.showMessage(`تم تعيين URL: ${button.textContent}`, 'success');
            };
        });

        // جلب النماذج المتاحة
        document.getElementById('hf-fetch-models').onclick = () => {
            this.fetchAvailableModels();
        };

        // تحديث النماذج
        document.getElementById('hf-refresh-models').onclick = () => {
            this.fetchAvailableModels(true);
        };
    }

    // ربط أحداث المنزلقات
    bindRangeInputs() {
        const ranges = ['max-tokens', 'temperature', 'top-p', 'repetition-penalty'];
        
        ranges.forEach(range => {
            const input = document.getElementById(`hf-${range}`);
            const display = document.getElementById(`hf-${range}-value`);
            
            input.oninput = () => {
                display.textContent = input.value;
            };
        });
    }

    // تحديث القيم الحالية
    updateCurrentValues() {
        const hfManager = window.huggingFaceManager;
        const status = hfManager.getStatus();

        // تحديث قائمة النماذج أولاً
        this.updateModelSelect();

        // تحديث التوكن
        if (status.hasToken) {
            document.getElementById('hf-api-token').value = hfManager.apiToken;
        }

        // تحديث إعدادات URL المخصص
        document.getElementById('hf-use-custom-url').checked = hfManager.useCustomURL;
        document.getElementById('hf-custom-api-url').value = hfManager.customAPIURL;

        // إظهار/إخفاء قسم URL المخصص
        const customUrlSection = document.getElementById('hf-custom-url-section');
        if (hfManager.useCustomURL) {
            customUrlSection.style.display = 'block';
        } else {
            customUrlSection.style.display = 'none';
        }

        // تحديث النموذج
        if (status.currentModel) {
            document.getElementById('hf-model-select').value = status.currentModel;
        }

        // تحديث إعدادات النموذج
        const settings = hfManager.settings;
        document.getElementById('hf-max-tokens').value = settings.maxTokens;
        document.getElementById('hf-max-tokens-value').textContent = settings.maxTokens;

        document.getElementById('hf-temperature').value = settings.temperature;
        document.getElementById('hf-temperature-value').textContent = settings.temperature;

        document.getElementById('hf-top-p').value = settings.topP;
        document.getElementById('hf-top-p-value').textContent = settings.topP;

        document.getElementById('hf-repetition-penalty').value = settings.repetitionPenalty;
        document.getElementById('hf-repetition-penalty-value').textContent = settings.repetitionPenalty;

        document.getElementById('hf-wait-for-model').checked = settings.waitForModel;
        document.getElementById('hf-use-cache').checked = settings.useCache;
        document.getElementById('hf-auto-detect-method').checked = hfManager.autoDetectMethod;

        // تحديث معلومات النموذج
        hfManager.updateCurrentModelInfo();
    }

    // حفظ الإعدادات
    saveSettings() {
        const hfManager = window.huggingFaceManager;

        // حفظ التوكن
        const token = document.getElementById('hf-api-token').value;
        hfManager.setToken(token);

        // حفظ إعدادات URL المخصص
        const useCustomURL = document.getElementById('hf-use-custom-url').checked;
        const customAPIURL = document.getElementById('hf-custom-api-url').value;

        hfManager.toggleCustomURL(useCustomURL);
        if (useCustomURL && customAPIURL) {
            hfManager.setCustomAPIURL(customAPIURL);
        }

        // حفظ النموذج
        const model = document.getElementById('hf-model-select').value;
        if (model) {
            hfManager.setModel(model);
        }

        // حفظ إعدادات النموذج
        const newSettings = {
            maxTokens: parseInt(document.getElementById('hf-max-tokens').value),
            temperature: parseFloat(document.getElementById('hf-temperature').value),
            topP: parseFloat(document.getElementById('hf-top-p').value),
            repetitionPenalty: parseFloat(document.getElementById('hf-repetition-penalty').value),
            waitForModel: document.getElementById('hf-wait-for-model').checked,
            useCache: document.getElementById('hf-use-cache').checked
        };

        // حفظ إعدادات الكشف التلقائي
        hfManager.autoDetectMethod = document.getElementById('hf-auto-detect-method').checked;

        hfManager.updateSettings(newSettings);
        hfManager.updateUI();

        // إظهار رسالة نجاح
        this.showMessage('تم حفظ الإعدادات بنجاح!', 'success');
    }

    // إعادة تعيين الإعدادات
    resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
            localStorage.removeItem('huggingface_settings');
            location.reload();
        }
    }

    // اختبار الاتصال
    async testConnection() {
        const hfManager = window.huggingFaceManager;
        const resultDiv = document.getElementById('hf-test-result');
        const testBtn = document.getElementById('hf-test-connection');

        // حفظ الإعدادات أولاً
        this.saveSettings();

        testBtn.disabled = true;
        testBtn.textContent = 'جاري الاختبار...';
        resultDiv.textContent = 'جاري اختبار الاتصال...';
        resultDiv.className = '';

        try {
            const result = await hfManager.testConnection();
            
            if (result.success) {
                resultDiv.textContent = result.message;
                resultDiv.className = 'hf-test-result success';
            } else {
                resultDiv.textContent = `فشل الاختبار: ${result.message}`;
                resultDiv.className = 'hf-test-result error';
            }
        } catch (error) {
            resultDiv.textContent = `خطأ في الاختبار: ${error.message}`;
            resultDiv.className = 'hf-test-result error';
        }

        testBtn.disabled = false;
        testBtn.textContent = 'اختبار الاتصال';
    }

    // إضافة نموذج مخصص
    addCustomModel() {
        const hfManager = window.huggingFaceManager;

        const modelId = document.getElementById('hf-custom-model-id').value.trim();
        const modelName = document.getElementById('hf-custom-model-name').value.trim();
        const modelDesc = document.getElementById('hf-custom-model-desc').value.trim();
        const modelType = document.getElementById('hf-custom-model-type').value;

        // التحقق من صحة البيانات
        if (!modelId) {
            this.showMessage('يرجى إدخال معرف النموذج', 'error');
            return;
        }

        if (!modelName) {
            this.showMessage('يرجى إدخال اسم النموذج', 'error');
            return;
        }

        if (!hfManager.validateModelId(modelId)) {
            this.showMessage('معرف النموذج غير صحيح. يجب أن يكون بالتنسيق: username/model-name', 'error');
            return;
        }

        // التحقق من عدم وجود النموذج مسبقاً
        if (hfManager.getModelInfo(modelId)) {
            this.showMessage('هذا النموذج موجود بالفعل', 'error');
            return;
        }

        try {
            // إضافة النموذج
            hfManager.addCustomModel(modelId, {
                name: modelName,
                description: modelDesc || 'نموذج مخصص',
                type: modelType
            });

            // تنظيف الحقول
            document.getElementById('hf-custom-model-id').value = '';
            document.getElementById('hf-custom-model-name').value = '';
            document.getElementById('hf-custom-model-desc').value = '';
            document.getElementById('hf-custom-model-type').value = 'text-generation';

            // تحديث قائمة النماذج
            this.updateModelSelect();

            this.showMessage('تم إضافة النموذج المخصص بنجاح!', 'success');
        } catch (error) {
            this.showMessage(`خطأ في إضافة النموذج: ${error.message}`, 'error');
        }
    }

    // تحديث قائمة النماذج
    updateModelSelect() {
        const hfManager = window.huggingFaceManager;
        const modelSelect = document.getElementById('hf-model-select');

        if (modelSelect) {
            const currentValue = modelSelect.value;
            modelSelect.innerHTML = `
                <option value="">-- اختر نموذج --</option>
                ${this.generateModelOptions(hfManager)}
            `;

            // استعادة القيمة المحددة إذا كانت لا تزال موجودة
            if (hfManager.getModelInfo(currentValue)) {
                modelSelect.value = currentValue;
            }
        }
    }

    // حذف نموذج مخصص
    removeCustomModel(modelId) {
        const hfManager = window.huggingFaceManager;

        if (confirm('هل أنت متأكد من حذف هذا النموذج المخصص؟')) {
            try {
                hfManager.removeCustomModel(modelId);
                this.updateModelSelect();
                hfManager.updateUI();
                this.showMessage('تم حذف النموذج المخصص بنجاح!', 'success');
            } catch (error) {
                this.showMessage(`خطأ في حذف النموذج: ${error.message}`, 'error');
            }
        }
    }

    // إظهار رسالة محسنة
    showMessage(message, type = 'info') {
        // إنشاء عنصر الرسالة
        const messageDiv = document.createElement('div');
        messageDiv.className = `hf-message hf-message-${type}`;
        messageDiv.innerHTML = `
            <div class="hf-message-content">
                <i class="fas ${this.getMessageIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // إضافة الأنماط
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getMessageColor(type)};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10001;
            animation: slideInRight 0.3s ease-out;
            max-width: 400px;
            word-wrap: break-word;
        `;

        // إضافة للصفحة
        document.body.appendChild(messageDiv);

        // إزالة بعد 4 ثوان
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }
        }, 4000);
    }

    // الحصول على أيقونة الرسالة
    getMessageIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // الحصول على لون الرسالة
    getMessageColor(type) {
        const colors = {
            'success': '#4CAF50',
            'error': '#f44336',
            'warning': '#FF9800',
            'info': '#2196F3'
        };
        return colors[type] || colors.info;
    }

    // جلب النماذج المتاحة
    async fetchAvailableModels(forceRefresh = false) {
        const hfManager = window.huggingFaceManager;

        // التحقق من وجود التوكن
        const token = document.getElementById('hf-api-token').value.trim();
        if (!token) {
            this.showMessage('يجب إدخال توكن API أولاً', 'error');
            return;
        }

        // حفظ التوكن مؤقت<|im_start|>
        hfManager.setToken(token);

        const fetchBtn = document.getElementById('hf-fetch-models');
        const refreshBtn = document.getElementById('hf-refresh-models');
        const progressDiv = document.getElementById('hf-fetch-progress');
        const progressFill = document.getElementById('hf-progress-fill');
        const progressText = document.getElementById('hf-progress-text');

        try {
            // إظهار حالة التحميل
            fetchBtn.disabled = true;
            fetchBtn.classList.add('loading');
            progressDiv.style.display = 'block';
            progressFill.style.width = '0%';
            progressText.textContent = 'بدء فحص النماذج...';

            // جلب النماذج مع مؤشر التقدم
            const models = await hfManager.fetchModelsWithProgress((progress) => {
                const percentage = (progress.current / progress.total) * 100;
                progressFill.style.width = `${percentage}%`;

                let statusText = '';
                if (progress.status === 'checking') {
                    statusText = `فحص ${progress.modelId}...`;
                } else if (progress.status === 'available') {
                    statusText = `✅ ${progress.modelId} متاح`;
                } else if (progress.status === 'unavailable') {
                    statusText = `❌ ${progress.modelId} غير متاح`;
                } else if (progress.status === 'error') {
                    statusText = `⚠️ خطأ في ${progress.modelId}`;
                }

                progressText.textContent = `${statusText} (${progress.current}/${progress.total})`;
            });

            // تحديث قائمة النماذج
            this.updateModelSelectWithFetched(models);

            // إظهار النتائج
            const modelCount = Object.keys(models).length;
            progressText.textContent = `✅ تم العثور على ${modelCount} نموذج متاح`;

            // إظهار زر التحديث
            refreshBtn.style.display = 'inline-flex';

            this.showMessage(`تم جلب ${modelCount} نموذج متاح بنجاح!`, 'success');

        } catch (error) {
            console.error('❌ خطأ في جلب النماذج:', error);
            progressText.textContent = `❌ فشل في جلب النماذج: ${error.message}`;
            this.showMessage(`فشل في جلب النماذج: ${error.message}`, 'error');
        } finally {
            // إخفاء حالة التحميل
            fetchBtn.disabled = false;
            fetchBtn.classList.remove('loading');

            // إخفاء شريط التقدم بعد 3 ثوان
            setTimeout(() => {
                progressDiv.style.display = 'none';
            }, 3000);
        }
    }

    // تحديث قائمة النماذج بالنماذج المجلبة
    updateModelSelectWithFetched(fetchedModels) {
        const hfManager = window.huggingFaceManager;
        const modelSelect = document.getElementById('hf-model-select');

        if (!modelSelect) return;

        const currentValue = modelSelect.value;

        // بناء خيارات جديدة
        let optionsHTML = '<option value="">-- اختر نموذج متاح --</option>';

        // النماذج المجلبة (متاحة ومؤكدة)
        if (Object.keys(fetchedModels).length > 0) {
            optionsHTML += '<optgroup label="🟢 النماذج المتاحة (مؤكدة)">';
            Object.entries(fetchedModels).forEach(([id, info]) => {
                optionsHTML += `<option value="${id}">✅ ${info.name} - ${info.description}</option>`;
            });
            optionsHTML += '</optgroup>';
        }

        // النماذج المخصصة
        const customModels = hfManager.getCustomModels();
        if (Object.keys(customModels).length > 0) {
            optionsHTML += '<optgroup label="⭐ النماذج المخصصة">';
            Object.entries(customModels).forEach(([id, info]) => {
                optionsHTML += `<option value="${id}">${info.name} - ${info.description} [مخصص]</option>`;
            });
            optionsHTML += '</optgroup>';
        }

        // النماذج الافتراضية (للمرجع)
        optionsHTML += '<optgroup label="📋 النماذج الافتراضية (غير مؤكدة)">';
        Object.entries(hfManager.getSupportedModels()).forEach(([id, info]) => {
            if (!fetchedModels[id]) { // فقط التي لم يتم فحصها
                optionsHTML += `<option value="${id}">❓ ${info.name} - ${info.description}</option>`;
            }
        });
        optionsHTML += '</optgroup>';

        modelSelect.innerHTML = optionsHTML;

        // استعادة القيمة المحددة إذا كانت لا تزال موجودة
        if (currentValue && (fetchedModels[currentValue] || hfManager.getModelInfo(currentValue))) {
            modelSelect.value = currentValue;
        }
    }
}

// إنشاء مثيل عام
window.huggingFaceSettings = new HuggingFaceSettings();
