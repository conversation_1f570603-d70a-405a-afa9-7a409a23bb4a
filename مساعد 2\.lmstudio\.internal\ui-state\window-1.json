{"plugins": {"selectedPluginIdentifier": null, "pluginsConfigTopPanelSizePercent": 25, "pluginsConfigBottomPanelSizePercent": 25, "pluginConfigNeedsPulsate": false}, "chat": {"pluginsPopoverIsOpen": false, "settingsPanelIsOpen": false, "chatsPanelIsOpen": true, "activeToolSidebarTab": "context", "perChat": {}, "expandedSidebarPlugins": [], "chatSidebarWidth": 300, "settingsSidebarWidth": 345, "terminalWidth": 600}, "developer": {"logsPanelSize": 30, "detailsPanelSize": 25, "panelGroupSize": 400, "isDetailsPanelCollapsed": false}, "modelLoading": {"sortType": "recent", "sortReversed": false}, "modelLoaderShowAdvancedSettings": false}