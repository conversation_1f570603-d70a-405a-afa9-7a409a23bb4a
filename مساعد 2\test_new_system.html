<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الجديد - Bug Bounty v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 10px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ اختبار النظام الجديد - Bug Bounty v4.0</h1>
            <p>نظام شامل مع البرومبت الكامل + صور حقيقية + اختبار فعلي</p>
        </div>

        <div class="test-section">
            <h2>🧪 اختبارات النظام الجديد</h2>
            
            <button class="test-button" onclick="testNewSystem()">
                🚀 اختبار النظام الجديد الشامل
            </button>
            
            <button class="test-button" onclick="testPromptAnalysis()">
                📄 اختبار تحليل البرومبت الكامل
            </button>
            
            <button class="test-button" onclick="testRealScreenshots()">
                📸 اختبار الصور الحقيقية
            </button>
            
            <button class="test-button" onclick="testRealVulnerabilities()">
                🔍 اختبار الثغرات الحقيقية
            </button>
            
            <button class="test-button" onclick="runFullScan()">
                🎯 فحص شامل حقيقي
            </button>
            
            <div id="results" class="results"></div>
        </div>

        <div class="test-section">
            <h2>📊 معلومات النظام</h2>
            <div id="systemInfo"></div>
        </div>
    </div>

    <!-- تحميل المكتبات المطلوبة -->
    <script src="assets/modules/bugbounty/impact_visualizer.js"></script>
    <script src="assets/modules/bugBounty/BugBountyCore.js"></script>
    <script src="assets/modules/bugbounty/prompt_only_system.js"></script>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString('ar');
            const className = type;
            results.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // اختبار النظام الجديد الشامل
        async function testNewSystem() {
            clearResults();
            log('🚀 بدء اختبار النظام الجديد الشامل...', 'info');
            
            try {
                // التحقق من وجود الكلاسات
                if (typeof BugBountyCore === 'undefined') {
                    log('❌ BugBountyCore غير محمل', 'error');
                    return;
                }
                
                const bugBounty = new BugBountyCore();
                log('✅ تم إنشاء BugBountyCore بنجاح', 'success');
                
                // اختبار تحميل البرومبت
                const prompt = await bugBounty.loadPromptTemplate();
                log(`✅ تم تحميل البرومبت: ${prompt.length} حرف`, 'success');
                
                // اختبار جمع البيانات
                const websiteData = await bugBounty.collectComprehensiveWebsiteData('http://testphp.vulnweb.com');
                log(`✅ تم جمع البيانات: ${Object.keys(websiteData).length} عنصر`, 'success');
                
                // اختبار التحليل الشامل
                const analysis = await bugBounty.generateProfessionalAnalysis(websiteData, 'http://testphp.vulnweb.com');
                log(`✅ تم إنشاء التحليل الشامل: ${analysis.length} حرف`, 'success');
                
                log('🎉 النظام الجديد يعمل بشكل ممتاز!', 'success');
                
            } catch (error) {
                log(`❌ خطأ في اختبار النظام: ${error.message}`, 'error');
            }
        }

        // اختبار تحليل البرومبت الكامل
        async function testPromptAnalysis() {
            clearResults();
            log('📄 بدء اختبار تحليل البرومبت الكامل...', 'info');
            
            try {
                const bugBounty = new BugBountyCore();
                
                // تحميل البرومبت
                const prompt = await bugBounty.loadPromptTemplate();
                log(`✅ تم تحميل البرومبت: ${prompt.length} حرف`, 'success');
                
                // عد أنواع الثغرات في البرومبت
                const vulnerabilityTypes = [
                    'SQL Injection', 'XSS', 'CSRF', 'IDOR', 'SSRF', 'XXE', 'SSTI',
                    'Authentication Bypass', 'Privilege Escalation', 'Path Traversal',
                    'Command Injection', 'LDAP Injection', 'NoSQL Injection'
                ];
                
                let foundTypes = 0;
                vulnerabilityTypes.forEach(type => {
                    if (prompt.includes(type)) {
                        foundTypes++;
                        log(`✅ تم العثور على: ${type}`, 'success');
                    }
                });
                
                log(`📊 إجمالي أنواع الثغرات في البرومبت: ${foundTypes}/${vulnerabilityTypes.length}`, 'info');
                
                if (foundTypes >= 10) {
                    log('🎉 البرومبت شامل ويحتوي على معظم أنواع الثغرات!', 'success');
                } else {
                    log('⚠️ البرومبت قد يحتاج المزيد من أنواع الثغرات', 'warning');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار البرومبت: ${error.message}`, 'error');
            }
        }

        // اختبار الصور الحقيقية
        async function testRealScreenshots() {
            clearResults();
            log('📸 بدء اختبار الصور الحقيقية...', 'info');
            
            try {
                const bugBounty = new BugBountyCore();
                
                // اختبار التقاط لقطة شاشة
                const screenshot = await bugBounty.captureWebsiteScreenshot('http://testphp.vulnweb.com', 'test_screenshot');
                
                if (screenshot && screenshot.screenshot_data) {
                    log('✅ تم التقاط لقطة شاشة بنجاح', 'success');
                    log(`📊 طريقة التقاط: ${screenshot.method}`, 'info');
                    log(`📅 التوقيت: ${screenshot.timestamp}`, 'info');
                    
                    if (screenshot.method === 'html2canvas') {
                        log('🎉 تم استخدام html2canvas للتقاط حقيقي!', 'success');
                    } else {
                        log('⚠️ تم استخدام طريقة بديلة للتقاط', 'warning');
                    }
                } else {
                    log('❌ فشل في التقاط لقطة شاشة', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار الصور: ${error.message}`, 'error');
            }
        }

        // اختبار الثغرات الحقيقية
        async function testRealVulnerabilities() {
            clearResults();
            log('🔍 بدء اختبار الثغرات الحقيقية...', 'info');
            
            try {
                const bugBounty = new BugBountyCore();
                
                // جمع البيانات
                const websiteData = await bugBounty.collectComprehensiveWebsiteData('http://testphp.vulnweb.com');
                log('✅ تم جمع البيانات', 'success');
                
                // تحليل شامل للثغرات
                const analysis = await bugBounty.performComprehensiveVulnerabilityAnalysis(websiteData, 'http://testphp.vulnweb.com', '');
                
                if (analysis && analysis.vulnerabilities) {
                    log(`✅ تم اكتشاف ${analysis.vulnerabilities.length} ثغرة`, 'success');
                    
                    const severityCounts = {
                        'Critical': 0,
                        'High': 0,
                        'Medium': 0,
                        'Low': 0
                    };
                    
                    analysis.vulnerabilities.forEach(vuln => {
                        severityCounts[vuln.severity] = (severityCounts[vuln.severity] || 0) + 1;
                        log(`🔍 ${vuln.name} - ${vuln.severity}`, 'info');
                    });
                    
                    log(`📊 توزيع الخطورة: Critical: ${severityCounts.Critical}, High: ${severityCounts.High}, Medium: ${severityCounts.Medium}, Low: ${severityCounts.Low}`, 'info');
                    
                } else {
                    log('⚠️ لم يتم اكتشاف ثغرات', 'warning');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار الثغرات: ${error.message}`, 'error');
            }
        }

        // فحص شامل حقيقي
        async function runFullScan() {
            clearResults();
            log('🎯 بدء فحص شامل حقيقي...', 'info');
            
            try {
                // استخدام الدالة الرئيسية
                if (typeof startComprehensiveBugBountyScan !== 'undefined') {
                    const chatContainer = document.getElementById('results');
                    await startComprehensiveBugBountyScan('http://testphp.vulnweb.com', chatContainer);
                    log('✅ تم إكمال الفحص الشامل', 'success');
                } else {
                    log('❌ دالة الفحص الشامل غير متاحة', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في الفحص الشامل: ${error.message}`, 'error');
            }
        }

        // عرض معلومات النظام
        function displaySystemInfo() {
            const systemInfo = document.getElementById('systemInfo');
            
            const info = {
                'BugBountyCore': typeof BugBountyCore !== 'undefined' ? '✅ محمل' : '❌ غير محمل',
                'ImpactVisualizer': typeof ImpactVisualizer !== 'undefined' ? '✅ محمل' : '❌ غير محمل',
                'PromptOnlyBugBountySystem': typeof PromptOnlyBugBountySystem !== 'undefined' ? '✅ محمل' : '❌ غير محمل',
                'startComprehensiveBugBountyScan': typeof startComprehensiveBugBountyScan !== 'undefined' ? '✅ متاح' : '❌ غير متاح',
                'html2canvas': typeof html2canvas !== 'undefined' ? '✅ متاح' : '❌ غير متاح'
            };
            
            let infoHtml = '<table style="width: 100%; border-collapse: collapse;">';
            infoHtml += '<tr style="background: #f0f0f0;"><th style="padding: 10px; border: 1px solid #ddd;">المكون</th><th style="padding: 10px; border: 1px solid #ddd;">الحالة</th></tr>';
            
            Object.entries(info).forEach(([key, value]) => {
                infoHtml += `<tr><td style="padding: 10px; border: 1px solid #ddd;">${key}</td><td style="padding: 10px; border: 1px solid #ddd;">${value}</td></tr>`;
            });
            
            infoHtml += '</table>';
            systemInfo.innerHTML = infoHtml;
        }

        // تشغيل عند تحميل الصفحة
        window.addEventListener('load', () => {
            displaySystemInfo();
            log('🎉 تم تحميل صفحة اختبار النظام الجديد', 'success');
        });
    </script>
</body>
</html>
