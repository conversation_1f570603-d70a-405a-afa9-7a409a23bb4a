<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكامل DeepSeek</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 10px;
            margin: 10px 0;
        }
        .success {
            background: rgba(46, 204, 113, 0.3);
            border: 1px solid #2ecc71;
        }
        .error {
            background: rgba(231, 76, 60, 0.3);
            border: 1px solid #e74c3c;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .provider-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .provider-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        .deepseek-highlight {
            background: rgba(255, 215, 0, 0.3);
            border: 2px solid #ffd700;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تكامل DeepSeek API</h1>
        
        <div class="test-section">
            <h3>📋 اختبار تحميل النماذج المدعومة</h3>
            <button onclick="testSupportedProviders()">اختبار النماذج المدعومة</button>
            <div id="providersResult"></div>
        </div>

        <div class="test-section">
            <h3>🔧 اختبار تكوين DeepSeek</h3>
            <button onclick="testDeepSeekConfig()">اختبار تكوين DeepSeek</button>
            <div id="configResult"></div>
        </div>

        <div class="test-section">
            <h3>🎯 اختبار ميزات DeepSeek</h3>
            <button onclick="testDeepSeekFeatures()">اختبار الميزات</button>
            <div id="featuresResult"></div>
        </div>

        <div class="test-section">
            <h3>🔌 اختبار واجهة API</h3>
            <button onclick="testAPIInterface()">فتح واجهة التكوين</button>
            <div id="interfaceResult"></div>
        </div>
    </div>

    <!-- تحميل الملفات المطلوبة -->
    <script src="assets/modules/api_integration/APIManager.js"></script>
    <script src="assets/modules/api_integration/APIConfigInterface.js"></script>

    <script>
        // اختبار النماذج المدعومة
        function testSupportedProviders() {
            const resultDiv = document.getElementById('providersResult');
            
            try {
                const providers = window.apiManager.supportedProviders;
                let html = '<div class="provider-list">';
                
                Object.entries(providers).forEach(([key, provider]) => {
                    const isDeepSeek = key === 'deepseek';
                    html += `
                        <div class="provider-card ${isDeepSeek ? 'deepseek-highlight' : ''}">
                            <strong>${provider.name}</strong><br>
                            <small>النوع: ${provider.type}</small><br>
                            <small>النماذج: ${provider.models.length}</small>
                            ${isDeepSeek ? '<br><span style="color: #ffd700;">✨ DeepSeek مضاف!</span>' : ''}
                        </div>
                    `;
                });
                
                html += '</div>';
                
                if (providers.deepseek) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ تم العثور على DeepSeek في النماذج المدعومة!
                            <br><strong>الاسم:</strong> ${providers.deepseek.name}
                            <br><strong>النماذج:</strong> ${providers.deepseek.models.join(', ')}
                            <br><strong>URL الأساسي:</strong> ${providers.deepseek.baseUrl}
                        </div>
                        ${html}
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ لم يتم العثور على DeepSeek في النماذج المدعومة
                        </div>
                        ${html}
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ خطأ في اختبار النماذج: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار تكوين DeepSeek
        function testDeepSeekConfig() {
            const resultDiv = document.getElementById('configResult');
            
            try {
                // محاولة تكوين DeepSeek بمفتاح وهمي
                const testConfig = {
                    apiKey: 'test-key-12345',
                    model: 'deepseek-chat'
                };
                
                window.apiManager.configureProvider('deepseek', testConfig);
                
                // التحقق من حفظ التكوين
                const savedConfig = window.apiManager.apiConfigs.deepseek;
                
                if (savedConfig && savedConfig.apiKey === testConfig.apiKey) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ تم تكوين DeepSeek بنجاح!
                            <br><strong>المفتاح:</strong> ${savedConfig.apiKey}
                            <br><strong>النموذج:</strong> ${savedConfig.model}
                            <br><strong>تاريخ التكوين:</strong> ${new Date(savedConfig.configuredAt).toLocaleString('ar')}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ فشل في حفظ تكوين DeepSeek
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ خطأ في تكوين DeepSeek: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار ميزات DeepSeek
        function testDeepSeekFeatures() {
            const resultDiv = document.getElementById('featuresResult');
            
            try {
                // تعيين DeepSeek كمزود حالي
                window.apiManager.currentProvider = 'deepseek';
                
                // اختبار ميزات النماذج
                const chatFeatures = window.apiManager.hasReasoningSupport('deepseek-chat');
                const reasonerFeatures = window.apiManager.hasReasoningSupport('deepseek-reasoner');
                const longContext = window.apiManager.hasLongContextSupport('deepseek-chat');
                
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        ✅ اختبار ميزات DeepSeek:
                        <br><strong>deepseek-chat - دعم التفكير:</strong> ${chatFeatures ? '✅ نعم' : '❌ لا'}
                        <br><strong>deepseek-reasoner - دعم التفكير:</strong> ${reasonerFeatures ? '✅ نعم' : '❌ لا'}
                        <br><strong>دعم السياق الطويل:</strong> ${longContext ? '✅ نعم' : '❌ لا'}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ خطأ في اختبار الميزات: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار واجهة API
        function testAPIInterface() {
            const resultDiv = document.getElementById('interfaceResult');
            
            try {
                if (window.APIConfigInterface) {
                    const configInterface = new APIConfigInterface();
                    configInterface.show();
                    
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ تم فتح واجهة تكوين API بنجاح!
                            <br>يجب أن ترى DeepSeek في قائمة النماذج المدعومة.
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ واجهة تكوين API غير متاحة
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ خطأ في فتح الواجهة: ${error.message}
                    </div>
                `;
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('🧪 بدء اختبار تكامل DeepSeek...');
            
            // التحقق من تحميل APIManager
            if (window.apiManager) {
                console.log('✅ تم تحميل APIManager بنجاح');
                console.log('📋 النماذج المدعومة:', Object.keys(window.apiManager.supportedProviders));
            } else {
                console.error('❌ فشل في تحميل APIManager');
            }
        });
    </script>
</body>
</html>
