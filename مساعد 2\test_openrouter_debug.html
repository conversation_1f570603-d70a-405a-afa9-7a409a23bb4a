<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص OpenRouter</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .result {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .success { background: rgba(46, 204, 113, 0.3); }
        .error { background: rgba(231, 76, 60, 0.3); }
        .warning { background: rgba(243, 156, 18, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة OpenRouter</h1>
        
        <div class="test-section">
            <h3>1. فحص حالة النظام</h3>
            <button onclick="checkSystemStatus()">فحص النظام</button>
            <div id="systemStatus" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار الاتصال المباشر</h3>
            <button onclick="testDirectConnection()">اختبار مباشر</button>
            <div id="directTest" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار التكامل الذكي</h3>
            <button onclick="testSmartIntegration()">اختبار التكامل</button>
            <div id="integrationTest" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار المحادثة النصية</h3>
            <input type="text" id="testMessage" placeholder="اكتب رسالة اختبار..." style="width: 70%; padding: 10px; border-radius: 5px; border: none;">
            <button onclick="testTextChat()">إرسال</button>
            <div id="chatTest" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. سجل الأخطاء</h3>
            <button onclick="showConsoleLog()">عرض السجل</button>
            <div id="consoleLog" class="result"></div>
        </div>
    </div>

    <script>
        let logs = [];
        
        // تسجيل جميع الرسائل
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            logs.push({type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logs.push({type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            logs.push({type: 'warn', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalWarn.apply(console, args);
        };

        function checkSystemStatus() {
            const result = document.getElementById('systemStatus');
            
            const status = {
                openRouterManager: !!window.openRouterManager,
                openRouterIntegration: !!window.openRouterIntegration,
                technicalAssistant: !!window.technicalAssistant,
                apiManager: !!window.apiManager
            };

            if (window.openRouterManager) {
                status.connectionStatus = window.openRouterManager.getConnectionStatus();
            }

            if (window.openRouterIntegration) {
                status.integrationInfo = window.openRouterIntegration.getIntegrationInfo();
            }

            result.textContent = JSON.stringify(status, null, 2);
            result.className = 'result ' + (status.openRouterManager ? 'success' : 'error');
        }

        async function testDirectConnection() {
            const result = document.getElementById('directTest');
            result.textContent = 'جاري الاختبار...';
            
            try {
                if (!window.openRouterManager) {
                    throw new Error('OpenRouter Manager غير محمل');
                }

                const response = await window.openRouterManager.sendMessage('مرحبا، هذا اختبار');
                
                result.textContent = `✅ نجح الاختبار المباشر:
النموذج: ${response.model}
الرد: ${response.text.substring(0, 200)}...
الوقت: ${new Date().toLocaleTimeString()}`;
                result.className = 'result success';

            } catch (error) {
                result.textContent = `❌ فشل الاختبار المباشر:
الخطأ: ${error.message}
التفاصيل: ${error.stack}`;
                result.className = 'result error';
            }
        }

        async function testSmartIntegration() {
            const result = document.getElementById('integrationTest');
            result.textContent = 'جاري اختبار التكامل...';
            
            try {
                if (!window.openRouterIntegration) {
                    throw new Error('OpenRouter Integration غير محمل');
                }

                const response = await window.openRouterIntegration.smartSendMessage('مرحبا، اختبار التكامل');
                
                result.textContent = `✅ نجح اختبار التكامل:
المصدر: ${response.source}
النموذج: ${response.model}
الرد: ${response.text.substring(0, 200)}...
الوقت: ${new Date().toLocaleTimeString()}`;
                result.className = 'result success';

            } catch (error) {
                result.textContent = `❌ فشل اختبار التكامل:
الخطأ: ${error.message}
التفاصيل: ${error.stack}`;
                result.className = 'result error';
            }
        }

        async function testTextChat() {
            const message = document.getElementById('testMessage').value;
            const result = document.getElementById('chatTest');
            
            if (!message.trim()) {
                result.textContent = 'يرجى كتابة رسالة اختبار';
                result.className = 'result warning';
                return;
            }

            result.textContent = 'جاري إرسال الرسالة...';
            
            try {
                // محاكاة نفس العملية التي تحدث في المحادثة النصية
                const response = await getInstantResponse(message);
                
                result.textContent = `✅ تم استلام الرد:
الرسالة: ${message}
الرد: ${response}
الوقت: ${new Date().toLocaleTimeString()}`;
                result.className = 'result success';

            } catch (error) {
                result.textContent = `❌ فشل في المحادثة النصية:
الرسالة: ${message}
الخطأ: ${error.message}
التفاصيل: ${error.stack}`;
                result.className = 'result error';
            }
        }

        function showConsoleLog() {
            const result = document.getElementById('consoleLog');
            
            const recentLogs = logs.slice(-20); // آخر 20 رسالة
            
            let logText = '';
            recentLogs.forEach(log => {
                logText += `[${log.time}] ${log.type.toUpperCase()}: ${log.message}\n`;
            });
            
            result.textContent = logText || 'لا توجد رسائل في السجل';
            result.className = 'result';
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkSystemStatus();
            }, 1000);
        });
    </script>
</body>
</html>
