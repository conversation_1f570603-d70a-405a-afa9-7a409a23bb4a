// تجربة جزء من الكود للفحص
class TestClass {
    // تحليل صفحة واحدة
    async analyzePage(url, htmlContent, headers) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // جمع النماذج
        const forms = Array.from(doc.querySelectorAll('form')).map(form => ({
            action: form.action || '',
            method: form.method || 'GET',
            inputs: Array.from(form.querySelectorAll('input, textarea, select')).map(input => ({
                name: input.name || '',
                type: input.type || 'text',
                value: input.value || '',
                required: input.required || false,
                placeholder: input.placeholder || ''
            }))
        }));

        return {
            url: url,
            title: doc.title || 'Untitled',
            forms: forms
        };
    }
}