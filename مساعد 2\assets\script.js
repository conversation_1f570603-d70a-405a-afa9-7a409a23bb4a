// ملف script.js - وظائف مساعدة بسيطة

// وظائف للتوافق مع الكود القديم
function addMessageToChat(sender, content) {
    if (window.addMessage) {
        addMessage(sender, content);
    }
}

function toggleVoiceRecording() {
    if (window.startVoiceRecording) {
        startVoiceRecording();
    }
}

function toggleVoiceSettings() {
    if (window.openVoiceSettings) {
        openVoiceSettings();
    }
}

// ربط أحداث الأزرار عند تحميل الصفحة - تم تعطيله لتجنب التضارب مع assistant-core.js
// سيتم ربط الأحداث من خلال assistant-core.js فقط
document.addEventListener('DOMContentLoaded', function() {
    console.log('📝 script.js محمل - ربط الأحداث سيتم من assistant-core.js');

    // انتظار تحميل assistant-core.js ثم إصلاح الأحداث
    setTimeout(() => {
        console.log('🔍 فحص الأزرار من script.js...');

        // اختبار مباشر للأزرار
        const testButtons = [
            { id: 'sendBtn', name: 'الإرسال' },
            { id: 'pureVoiceBtn', name: 'المحادثة الخالصة' },
            { id: 'voiceBtn', name: 'الصوت العادي' },
            { id: 'screenShareBtn', name: 'مشاركة الشاشة' }
        ];

        testButtons.forEach(({ id, name }) => {
            const btn = document.getElementById(id);
            if (btn) {
                console.log(`✅ ${name} (${id}): موجود`);
                console.log(`   - onclick: ${btn.onclick ? 'مربوط' : 'غير مربوط'}`);
                console.log(`   - disabled: ${btn.disabled}`);
                console.log(`   - style.display: ${btn.style.display || 'default'}`);
                console.log(`   - style.pointerEvents: ${btn.style.pointerEvents || 'default'}`);

                // اختبار ربط مباشر
                if (!btn.onclick) {
                    console.log(`⚠️ ${name} غير مربوط - محاولة ربط مباشر...`);
                    if (id === 'sendBtn' && window.sendMessage) {
                        btn.onclick = window.sendMessage;
                        console.log(`✅ تم ربط ${name} مباشرة`);
                    } else if (id === 'pureVoiceBtn' && window.togglePureVoiceMode) {
                        btn.onclick = window.togglePureVoiceMode;
                        console.log(`✅ تم ربط ${name} مباشرة`);
                    }
                }
            } else {
                console.error(`❌ ${name} (${id}): غير موجود في DOM`);
            }
        });

        if (window.fixButtonEvents) {
            console.log('🔧 إصلاح إضافي للأحداث من script.js');
            window.fixButtonEvents();
        }
    }, 2000);
});



    // تم نقل ربط الأحداث إلى assistant-core.js لتجنب التضارب
    console.log('✅ script.js جاهز - الأحداث ستُربط من assistant-core.js');

function closeSettingsModal() {
    if (window.closeSettings) {
        closeSettings();
    }
}

function closeDisplayArea() {
    const displayArea = document.getElementById('displayArea');
    if (displayArea) {
        displayArea.style.display = 'none';
    }
}

// دالة تفعيل/إلغاء تفعيل نظام التحسين الذاتي
function toggleAISelfImprovement() {
    const aiImproveBtn = document.getElementById('aiImproveBtn');
    const aiImproveInputBtn = document.getElementById('aiImproveInputBtn');

    if (!window.aiSelfImprove) {
        console.error('❌ نظام التحسين الذاتي غير متاح');
        return;
    }

    if (window.aiSelfImprove.isActive) {
        // إلغاء التفعيل
        window.aiSelfImprove.deactivate();

        // تحديث الزر في الشريط الجانبي
        if (aiImproveBtn) {
            aiImproveBtn.classList.remove('active');
            aiImproveBtn.innerHTML = '<i class="fas fa-robot"></i><span>التحسين الذاتي</span>';
            aiImproveBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - غير نشط';
        }

        // تحديث الزر في منطقة الإدخال
        if (aiImproveInputBtn) {
            aiImproveInputBtn.classList.remove('active');
            aiImproveInputBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - غير نشط';
        }

        // إشعار صوتي
        if (typeof speakText === 'function') {
            speakText('تم إيقاف نظام التحسين الذاتي', {
                emotion: 'neutral',
                context: 'system'
            });
        }

        console.log('⏹️ تم إيقاف نظام التحسين الذاتي');

    } else {
        // التفعيل
        window.aiSelfImprove.activate();

        // تحديث الزر في الشريط الجانبي
        if (aiImproveBtn) {
            aiImproveBtn.classList.add('active');
            aiImproveBtn.innerHTML = '<i class="fas fa-robot"></i><span>نشط - التحسين الذاتي</span>';
            aiImproveBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - نشط';
        }

        // تحديث الزر في منطقة الإدخال
        if (aiImproveInputBtn) {
            aiImproveInputBtn.classList.add('active');
            aiImproveInputBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - نشط';
        }

        // إشعار صوتي
        if (typeof speakText === 'function') {
            speakText('تم تفعيل نظام التحسين الذاتي. سأراقب الكود وأطلب المساعدة من الذكاء الاصطناعي عند الحاجة', {
                emotion: 'excited',
                context: 'system'
            });
        }

        console.log('🚀 تم تفعيل نظام التحسين الذاتي');

        // عرض واجهة التحسين
        setTimeout(() => {
            window.aiSelfImprove.showInterface();
        }, 1000);
    }
}

// دالة فتح واجهة التحسين الذاتي (للاستخدام المباشر)
function openAISelfImprovement() {
    if (window.aiSelfImprove) {
        window.aiSelfImprove.showInterface();
    } else {
        console.error('❌ نظام التحسين الذاتي غير متاح');
    }
}

// دالة إنشاء زر التحسين الذاتي إذا لم يوجد
function createAIImproveButtonIfMissing() {
    const existingBtn = document.getElementById('aiImproveBtn');
    if (existingBtn) {
        console.log('✅ زر التحسين الذاتي موجود بالفعل');
        return;
    }

    console.log('🔧 إنشاء زر التحسين الذاتي...');

    // البحث عن الحاوية المناسبة
    const toolsContainer = document.querySelector('.tools-container');
    const sidebar = document.querySelector('.sidebar');

    if (toolsContainer || sidebar) {
        const container = toolsContainer || sidebar;

        // إنشاء الزر
        const aiImproveBtn = document.createElement('button');
        aiImproveBtn.className = 'tool-btn ai-improve-btn';
        aiImproveBtn.id = 'aiImproveBtn';
        aiImproveBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - غير نشط';
        aiImproveBtn.style.display = 'flex';
        aiImproveBtn.innerHTML = `
            <i class="fas fa-robot"></i>
            <span>التحسين الذاتي</span>
        `;

        // إضافة الزر للحاوية
        container.appendChild(aiImproveBtn);

        // ربط الأحداث
        aiImproveBtn.addEventListener('click', function() {
            console.log('🤖 تم النقر على زر التحسين الذاتي (المنشأ ديناميكياً)');
            if (window.aiSelfImprove) {
                toggleAISelfImprovement();
            } else {
                console.error('❌ نظام التحسين الذاتي غير متاح');
            }
        });

        console.log('✅ تم إنشاء زر التحسين الذاتي بنجاح');
    } else {
        console.error('❌ لم يتم العثور على حاوية مناسبة لإضافة الزر');
    }
}

// تصدير الدوال للاستخدام العام
window.toggleAISelfImprovement = toggleAISelfImprovement;
window.openAISelfImprovement = openAISelfImprovement;
window.createAIImproveButtonIfMissing = createAIImproveButtonIfMissing;

// تصدير للتوافق
window.addMessageToChat = addMessageToChat;
