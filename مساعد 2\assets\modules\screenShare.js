// وحدة مشاركة الشاشة
// Screen Sharing Module

class ScreenShareManager {
    constructor() {
        this.mediaStream = null;
        this.isSharing = false;
        this.recordedChunks = [];
        this.mediaRecorder = null;
        this.securityAnalysisMode = false; // وضع التحليل الأمني
        this.bugBountyIntegration = true; // تكامل مع Bug Bounty Mode
        this.realTimeAnalysis = false; // التحليل المباشر
        this.voiceInitialized = false; // حالة تهيئة النظام الصوتي
    }

    // تهيئة النظام الصوتي المتقدم الأصلي بكامل إمكانياته
    async initializeAdvancedVoice() {
        try {
            console.log('🎤 تهيئة النظام الصوتي المتقدم الأصلي بكامل إمكانياته...');

            // تحميل النظام الصوتي المتقدم الأصلي إذا لم يكن محملاً
            if (!window.AdvancedVoiceEngine) {
                await this.loadAdvancedVoiceEngine();
            }

            // تهيئة النظام الصوتي المتقدم الأصلي بكامل قوته
            if (window.AdvancedVoiceEngine && !window.advancedVoiceEngine) {
                window.advancedVoiceEngine = new window.AdvancedVoiceEngine();
                await window.advancedVoiceEngine.initialize();

                // تفعيل جميع الميزات المتقدمة مثل ChatGPT Pro
                window.advancedVoiceEngine.continuousMode = true;
                window.advancedVoiceEngine.realTimeMode = true;
                window.advancedVoiceEngine.voiceActivation = true;
                window.advancedVoiceEngine.smartSilenceDetection = true;
                window.advancedVoiceEngine.interruptionHandling = true;
                window.advancedVoiceEngine.enhancedClarity = true;
                window.advancedVoiceEngine.professionalMode = true;
                window.advancedVoiceEngine.naturalPauses = true;
                window.advancedVoiceEngine.emotionalIntonation = true;
                window.advancedVoiceEngine.contextualAdaptation = true;

                // تعيين اللهجة العراقية الاحترافية
                if (typeof window.advancedVoiceEngine.setDialect === 'function') {
                    window.advancedVoiceEngine.setDialect('iraqi');
                }

                // تعيين النمط الاحترافي العراقي
                if (typeof window.advancedVoiceEngine.setVoiceProfile === 'function') {
                    window.advancedVoiceEngine.setVoiceProfile('iraqi_professional');
                }

                console.log('✅ تم تفعيل جميع الميزات المتقدمة مثل ChatGPT Pro');
            }

            // تفعيل الاستماع المتقدم المستمر مع جميع المعالجات
            if (window.advancedVoiceEngine && typeof window.advancedVoiceEngine.startAdvancedListening === 'function') {
                const screenShareInstance = this;

                await window.advancedVoiceEngine.startAdvancedListening({
                    onStart: () => {
                        console.log('🎧 بدء الاستماع المتقدم المستمر في مشاركة الشاشة');
                        screenShareInstance.voiceInitialized = true;

                        // إشعار صوتي بالبدء
                        if (window.advancedVoiceEngine.speak) {
                            window.advancedVoiceEngine.speak('أهلين! النظام الصوتي المتقدم جاهز للتفاعل المستمر معك.', {
                                emotion: 'friendly',
                                priority: 'high'
                            });
                        }
                    },
                    onInput: async (text) => {
                        console.log('🎤 مدخل صوتي من النظام المتقدم:', text);
                        // استخدام الوظيفة الموجودة في النظام المتقدم
                        return await screenShareInstance.handleScreenShareVoiceInput(text);
                    },
                    onInterim: (text) => {
                        console.log('🎤 نص مؤقت:', text);
                        // عرض النص المؤقت في الواجهة
                        screenShareInstance.updateVoiceStatus(`🎤 أسمع: "${text}"`);
                    },
                    onSilence: () => {
                        console.log('🔇 صمت مكتشف - جاهز للاستماع مرة أخرى');
                        screenShareInstance.updateVoiceStatus('🎧 أستمع إليك...');
                    },
                    onError: (error) => {
                        console.error('❌ خطأ في الاستماع المتقدم:', error);

                        // إعادة تشغيل تلقائي مع معالجة ذكية للأخطاء
                        if (error === 'not-allowed') {
                            console.warn('⚠️ المستخدم لم يسمح بالوصول للميكروفون');
                            if (window.advancedVoiceEngine.speak) {
                                window.advancedVoiceEngine.speak('يرجى السماح بالوصول للميكروفون لتفعيل التفاعل الصوتي.');
                            }
                        } else {
                            setTimeout(() => {
                                if (window.advancedVoiceEngine && screenShareInstance.voiceInitialized) {
                                    console.log('🔄 إعادة تشغيل الاستماع المتقدم تلقائياً...');
                                    window.advancedVoiceEngine.startAdvancedListening({
                                        onInput: async (text) => {
                                            return await screenShareInstance.handleScreenShareVoiceInput(text);
                                        }
                                    });
                                }
                            }, 2000);
                        }
                    }
                });

                console.log('🎧 تم تفعيل الاستماع المتقدم المستمر بنجاح');
            } else {
                console.error('❌ وظيفة startAdvancedListening غير متاحة في النظام المتقدم');
                return false;
            }

            this.voiceInitialized = true;
            console.log('✅ تم تهيئة النظام الصوتي المتقدم بكامل إمكانياته');

            // رسالة ترحيب تفاعلية بالنظام المتقدم مع تأكيد النماذج
            if (window.advancedVoiceEngine && typeof window.advancedVoiceEngine.speak === 'function') {
                const availableModels = this.checkAvailableModels();
                let welcomeMessage = 'أهلين وسهلين! تم تفعيل النظام الصوتي المتقدم مع مشاركة الشاشة بكامل قوته. ';

                if (availableModels.getAIModelResponse || availableModels.technicalAssistant) {
                    welcomeMessage += 'النماذج الذكية تعمل بشكل ممتاز وجاهزة للتفاعل معك. ';
                } else {
                    welcomeMessage += 'تحذير: النماذج الذكية غير متاحة حالياً. ';
                }

                welcomeMessage += 'أنا الآن جاهز للتفاعل معك بشكل مستمر وطبيعي مثل ChatGPT المدفوع. تحدث معي في أي وقت وسأرد عليك فوراً بذكاء واحترافية.';

                await window.advancedVoiceEngine.speak(welcomeMessage, {
                    emotion: 'enthusiastic',
                    context: 'screen_share_welcome',
                    enhanceWithAI: false, // تعطيل التحسين لتجنب المشاكل
                    naturalPauses: true,
                    emotionalIntonation: true
                });
            }

            // تحقق شامل من حالة النظام
            console.log('🔍 تقرير حالة النظام الصوتي المتقدم:');
            console.log('  ✅ AdvancedVoiceEngine متاح:', !!window.AdvancedVoiceEngine);
            console.log('  ✅ advancedVoiceEngine مهيأ:', !!window.advancedVoiceEngine);
            console.log('  ✅ isInitialized:', window.advancedVoiceEngine?.isInitialized);
            console.log('  ✅ continuousMode:', window.advancedVoiceEngine?.continuousMode);
            console.log('  ✅ realTimeMode:', window.advancedVoiceEngine?.realTimeMode);
            console.log('  ✅ isListening:', window.advancedVoiceEngine?.isListening);
            console.log('  ✅ voiceActivation:', window.advancedVoiceEngine?.voiceActivation);

            return true;

        } catch (error) {
            console.error('❌ خطأ في تهيئة النظام الصوتي المتقدم:', error);
            this.voiceInitialized = false;
            return false;
        }
    }

    // تحميل النظام الصوتي المتقدم الأصلي
    async loadAdvancedVoiceEngine() {
        return new Promise((resolve, reject) => {
            if (window.AdvancedVoiceEngine) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'assets/modules/voice/AdvancedVoiceEngine.js';
            script.onload = () => {
                console.log('✅ تم تحميل النظام الصوتي المتقدم الأصلي');
                resolve();
            };
            script.onerror = () => {
                console.error('❌ فشل في تحميل النظام الصوتي المتقدم الأصلي');
                reject();
            };
            document.head.appendChild(script);
        });
    }

    // فحص النماذج المتاحة
    checkAvailableModels() {
        console.log('🔍 فحص النماذج المتاحة...');

        const models = {
            getAIModelResponse: typeof window.getAIModelResponse === 'function',
            technicalAssistant: typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse,
            openRouter: window.openRouterIntegration && window.openRouterIntegration.isEnabled,
            huggingFace: window.huggingFaceManager && window.huggingFaceManager.isEnabled,
            advancedVoice: window.AdvancedVoiceEngine || window.advancedVoiceEngine
        };

        console.log('📊 تقرير النماذج المتاحة:');
        console.log('  🔧 getAIModelResponse:', models.getAIModelResponse ? '✅ متاح' : '❌ غير متاح');
        console.log('  🤖 technicalAssistant:', models.technicalAssistant ? '✅ متاح' : '❌ غير متاح');
        console.log('  🔗 OpenRouter:', models.openRouter ? '✅ متاح' : '❌ غير متاح');
        console.log('  🤗 Hugging Face:', models.huggingFace ? '✅ متاح' : '❌ غير متاح');
        console.log('  🎤 Advanced Voice:', models.advancedVoice ? '✅ متاح' : '❌ غير متاح');

        if (!models.getAIModelResponse && !models.technicalAssistant && !models.openRouter && !models.huggingFace) {
            console.error('❌ خطأ: لا توجد نماذج ذكية متاحة للتفاعل!');
            addMessageToChat('assistant', '⚠️ تحذير: النماذج الذكية غير متاحة في مشاركة الشاشة. سيتم استخدام ردود افتراضية.');
        } else {
            console.log('✅ يوجد نماذج متاحة للتفاعل');
            addMessageToChat('assistant', '✅ النماذج الذكية جاهزة ومتاحة للتفاعل في مشاركة الشاشة.');
        }

        return models;
    }

    // اختبار النماذج المتاحة
    async testAvailableModels() {
        console.log('🧪 اختبار النماذج المتاحة...');

        // اختبار getAIModelResponse
        if (typeof window.getAIModelResponse === 'function') {
            try {
                console.log('🧪 اختبار getAIModelResponse...');
                const testResponse = await window.getAIModelResponse('مرحبا');
                console.log('✅ getAIModelResponse يعمل:', testResponse?.substring(0, 50));
            } catch (error) {
                console.error('❌ getAIModelResponse لا يعمل:', error);
            }
        }

        // اختبار technicalAssistant
        if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
            try {
                console.log('🧪 اختبار technicalAssistant...');
                const testResponse = await technicalAssistant.getResponse('مرحبا');
                console.log('✅ technicalAssistant يعمل:', testResponse?.substring(0, 50));
            } catch (error) {
                console.error('❌ technicalAssistant لا يعمل:', error);
            }
        }

        // اختبار OpenRouter
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            try {
                console.log('🧪 اختبار OpenRouter...');
                const testResponse = await window.openRouterIntegration.smartSendMessage('مرحبا');
                console.log('✅ OpenRouter يعمل:', testResponse?.text?.substring(0, 50));
            } catch (error) {
                console.error('❌ OpenRouter لا يعمل:', error);
            }
        }
    }

    // بدء مشاركة الشاشة
    async startScreenShare() {
        try {
            console.log('🖥️ بدء مشاركة الشاشة...');

            // 🔍 تحقق من وجود النماذج المتاحة واختبارها
            this.checkAvailableModels();
            await this.testAvailableModels();

            // 🎤 تفعيل النظام الصوتي المتقدم أولاً
            await this.initializeAdvancedVoice();

            // طلب إذن مشاركة الشاشة
            this.mediaStream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    mediaSource: 'screen',
                    width: { ideal: 1920 },
                    height: { ideal: 1080 },
                    frameRate: { ideal: 30 }
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: 44100
                }
            });

            this.isSharing = true;
            console.log('✅ تم بدء مشاركة الشاشة بنجاح');

            // عرض الشاشة المشاركة
            this.displaySharedScreen();

            // إعداد التسجيل
            this.setupRecording();

            // مراقبة إيقاف المشاركة
            this.mediaStream.getVideoTracks()[0].addEventListener('ended', () => {
                this.stopScreenShare();
            });

            // إضافة رسالة للمحادثة
            addMessageToChat('assistant', 'تم بدء مشاركة الشاشة بنجاح! يمكنك الآن عرض شاشتك وتسجيلها.');

            // تفعيل التفاعل الصوتي
            this.enableVoiceInteraction();

            return true;

        } catch (error) {
            console.error('❌ خطأ في مشاركة الشاشة:', error);
            this.handleScreenShareError(error);
            return false;
        }
    }

    // عرض الشاشة المشاركة
    displaySharedScreen() {
        const displayArea = document.getElementById('displayArea');
        const displayContent = document.getElementById('displayContent');
        const displayTitle = document.getElementById('displayTitle');

        // إنشاء عنصر الفيديو
        const videoElement = document.createElement('video');
        videoElement.id = 'sharedScreenVideo';
        videoElement.autoplay = true;
        videoElement.muted = true;
        videoElement.style.width = '100%';
        videoElement.style.height = 'auto';
        videoElement.style.borderRadius = '8px';
        videoElement.srcObject = this.mediaStream;

        // إنشاء أزرار التحكم
        const controlsDiv = document.createElement('div');
        controlsDiv.className = 'screen-share-controls';
        controlsDiv.style.marginTop = '15px';
        controlsDiv.style.display = 'flex';
        controlsDiv.style.gap = '10px';
        controlsDiv.style.justifyContent = 'center';

        // زر التسجيل
        const recordBtn = document.createElement('button');
        recordBtn.innerHTML = '<i class="fas fa-record-vinyl"></i> بدء التسجيل';
        recordBtn.className = 'tool-btn';
        recordBtn.style.fontSize = '0.9rem';
        recordBtn.onclick = () => this.toggleRecording();

        // زر لقطة الشاشة
        const screenshotBtn = document.createElement('button');
        screenshotBtn.innerHTML = '<i class="fas fa-camera"></i> لقطة شاشة';
        screenshotBtn.className = 'tool-btn';
        screenshotBtn.style.fontSize = '0.9rem';
        screenshotBtn.onclick = () => this.takeScreenshot();

        // زر التحليل الأمني
        const securityBtn = document.createElement('button');
        securityBtn.innerHTML = '<i class="fas fa-shield-alt"></i> تحليل أمني';
        securityBtn.className = 'tool-btn';
        securityBtn.style.fontSize = '0.9rem';
        securityBtn.style.background = '#e74c3c';
        securityBtn.onclick = () => this.toggleSecurityAnalysis();

        // زر إيقاف المشاركة
        const stopBtn = document.createElement('button');
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف المشاركة';
        stopBtn.className = 'tool-btn';
        stopBtn.style.fontSize = '0.9rem';
        stopBtn.style.background = '#ff4757';
        stopBtn.onclick = () => this.stopScreenShare();

        controlsDiv.appendChild(recordBtn);
        controlsDiv.appendChild(screenshotBtn);
        controlsDiv.appendChild(securityBtn);
        controlsDiv.appendChild(stopBtn);

        // تحديث منطقة العرض
        displayTitle.textContent = 'مشاركة الشاشة';
        displayContent.innerHTML = '';
        displayContent.appendChild(videoElement);
        displayContent.appendChild(controlsDiv);
        displayArea.style.display = 'flex';
    }

    // إعداد التسجيل
    setupRecording() {
        try {
            this.mediaRecorder = new MediaRecorder(this.mediaStream, {
                mimeType: 'video/webm;codecs=vp9'
            });

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.saveRecording();
            };

            console.log('✅ تم إعداد التسجيل');
        } catch (error) {
            console.error('❌ خطأ في إعداد التسجيل:', error);
        }
    }

    // تبديل التسجيل
    toggleRecording() {
        if (!this.mediaRecorder) {
            addMessageToChat('assistant', 'خطأ: لم يتم إعداد التسجيل بشكل صحيح');
            return;
        }

        if (this.mediaRecorder.state === 'recording') {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    // بدء التسجيل
    startRecording() {
        try {
            this.recordedChunks = [];
            this.mediaRecorder.start(1000); // تسجيل كل ثانية
            
            // تحديث زر التسجيل
            const recordBtn = document.querySelector('.screen-share-controls button');
            if (recordBtn) {
                recordBtn.innerHTML = '<i class="fas fa-stop-circle"></i> إيقاف التسجيل';
                recordBtn.style.background = '#ff4757';
            }

            addMessageToChat('assistant', 'تم بدء تسجيل الشاشة');
            console.log('🔴 بدء تسجيل الشاشة');
        } catch (error) {
            console.error('❌ خطأ في بدء التسجيل:', error);
        }
    }

    // إيقاف التسجيل
    stopRecording() {
        try {
            this.mediaRecorder.stop();
            
            // تحديث زر التسجيل
            const recordBtn = document.querySelector('.screen-share-controls button');
            if (recordBtn) {
                recordBtn.innerHTML = '<i class="fas fa-record-vinyl"></i> بدء التسجيل';
                recordBtn.style.background = '';
            }

            addMessageToChat('assistant', 'تم إيقاف التسجيل وحفظ الملف');
            console.log('⏹️ إيقاف تسجيل الشاشة');
        } catch (error) {
            console.error('❌ خطأ في إيقاف التسجيل:', error);
        }
    }

    // حفظ التسجيل
    saveRecording() {
        if (this.recordedChunks.length === 0) {
            console.warn('⚠️ لا توجد بيانات للحفظ');
            return;
        }

        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `تسجيل_الشاشة_${new Date().toISOString().split('T')[0]}.webm`;
        a.click();
        
        URL.revokeObjectURL(url);
        console.log('💾 تم حفظ التسجيل');
    }

    // أخذ لقطة شاشة
    takeScreenshot() {
        try {
            const video = document.getElementById('sharedScreenVideo');
            if (!video) {
                addMessageToChat('assistant', 'خطأ: لا يمكن العثور على الفيديو');
                return;
            }

            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            
            canvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `لقطة_شاشة_${new Date().toISOString().split('T')[0]}.png`;
                a.click();
                URL.revokeObjectURL(url);
            }, 'image/png');

            addMessageToChat('assistant', 'تم حفظ لقطة الشاشة');
            console.log('📸 تم أخذ لقطة شاشة');
        } catch (error) {
            console.error('❌ خطأ في أخذ لقطة الشاشة:', error);
        }
    }

    // إيقاف مشاركة الشاشة مع إغلاق النظام الصوتي بشكل كامل
    stopScreenShare() {
        try {
            console.log('⏹️ بدء إيقاف مشاركة الشاشة والنظام الصوتي...');

            // إيقاف النظام الصوتي المتقدم بشكل كامل
            this.stopAdvancedVoiceSystem();

            // إيقاف مشاركة الشاشة
            if (this.mediaStream) {
                this.mediaStream.getTracks().forEach(track => {
                    track.stop();
                    console.log('🔇 تم إيقاف مسار:', track.kind);
                });
                this.mediaStream = null;
            }

            // إيقاف التسجيل
            if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
                this.mediaRecorder.stop();
                console.log('🔴 تم إيقاف التسجيل');
            }

            // إيقاف التحليل المباشر
            if (this.analysisInterval) {
                clearInterval(this.analysisInterval);
                this.analysisInterval = null;
                console.log('🔍 تم إيقاف التحليل المباشر');
            }

            // إعادة تعيين الحالات
            this.isSharing = false;
            this.voiceInitialized = false;
            this.securityAnalysisMode = false;
            this.realTimeAnalysis = false;

            // إخفاء منطقة العرض
            const displayArea = document.getElementById('displayArea');
            if (displayArea) {
                displayArea.style.display = 'none';
            }

            // إزالة عنصر حالة الصوت
            const voiceStatus = document.getElementById('voiceStatus');
            if (voiceStatus) {
                voiceStatus.remove();
                console.log('🗑️ تم إزالة عنصر حالة الصوت');
            }

            addMessageToChat('assistant', '✅ تم إيقاف مشاركة الشاشة والنظام الصوتي بشكل كامل');
            console.log('✅ تم إيقاف مشاركة الشاشة والنظام الصوتي بشكل كامل');

        } catch (error) {
            console.error('❌ خطأ في إيقاف مشاركة الشاشة:', error);
        }
    }

    // إيقاف النظام الصوتي المتقدم بشكل كامل
    stopAdvancedVoiceSystem() {
        try {
            console.log('🔇 إيقاف النظام الصوتي المتقدم بشكل كامل...');

            if (window.advancedVoiceEngine) {
                // إيقاف الاستماع
                if (typeof window.advancedVoiceEngine.stopAdvancedListening === 'function') {
                    window.advancedVoiceEngine.stopAdvancedListening();
                    console.log('🎧 تم إيقاف الاستماع المتقدم');
                }

                // إيقاف النطق
                if (typeof window.advancedVoiceEngine.stop === 'function') {
                    window.advancedVoiceEngine.stop();
                    console.log('🗣️ تم إيقاف النطق');
                }

                // إيقاف جميع الوضعيات
                window.advancedVoiceEngine.continuousMode = false;
                window.advancedVoiceEngine.realTimeMode = false;
                window.advancedVoiceEngine.voiceActivation = false;
                window.advancedVoiceEngine.isListening = false;

                // تنظيف المعالجات
                window.advancedVoiceEngine.onInput = null;
                window.advancedVoiceEngine.onInterim = null;
                window.advancedVoiceEngine.onSilence = null;
                window.advancedVoiceEngine.onError = null;

                console.log('✅ تم إيقاف النظام الصوتي المتقدم بشكل كامل');
            }

            // تنظيف المتغيرات
            this.voiceInitialized = false;

        } catch (error) {
            console.error('❌ خطأ في إيقاف النظام الصوتي المتقدم:', error);
        }
    }

    // معالجة أخطاء مشاركة الشاشة
    handleScreenShareError(error) {
        let errorMessage = 'حدث خطأ في مشاركة الشاشة';
        
        switch (error.name) {
            case 'NotAllowedError':
                errorMessage = 'تم رفض الإذن لمشاركة الشاشة';
                break;
            case 'NotFoundError':
                errorMessage = 'لم يتم العثور على شاشة للمشاركة';
                break;
            case 'NotSupportedError':
                errorMessage = 'المتصفح لا يدعم مشاركة الشاشة';
                break;
            case 'AbortError':
                errorMessage = 'تم إلغاء مشاركة الشاشة';
                break;
        }
        
        addMessageToChat('assistant', `خطأ: ${errorMessage}`);
        console.error('❌', errorMessage, error);
    }

    // تبديل وضع التحليل الأمني
    toggleSecurityAnalysis() {
        this.securityAnalysisMode = !this.securityAnalysisMode;

        const securityBtn = document.querySelector('.screen-share-controls button:nth-child(3)');

        if (this.securityAnalysisMode) {
            securityBtn.innerHTML = '<i class="fas fa-shield-alt"></i> إيقاف التحليل';
            securityBtn.style.background = '#27ae60';
            this.startSecurityAnalysis();
        } else {
            securityBtn.innerHTML = '<i class="fas fa-shield-alt"></i> تحليل أمني';
            securityBtn.style.background = '#e74c3c';
            this.stopSecurityAnalysis();
        }
    }

    // بدء التحليل الأمني للشاشة
    startSecurityAnalysis() {
        console.log('🔒 بدء التحليل الأمني للشاشة...');

        // تفعيل Bug Bounty Mode إذا لم يكن نشطاً
        if (window.BugBountyCore && !window.bugBountyInstance) {
            window.bugBountyInstance = new BugBountyCore();
        }

        if (window.bugBountyInstance && !window.bugBountyInstance.isActive) {
            window.bugBountyInstance.activate();
        }

        // بدء التحليل المباشر
        this.realTimeAnalysis = true;
        this.startRealTimeAnalysis();

        // إضافة رسالة للمحادثة
        if (typeof addMessage === 'function') {
            addMessage('assistant', `🔒 **تم تفعيل التحليل الأمني للشاشة!**

🎯 **الميزات النشطة:**
• تحليل مباشر للمحتوى المعروض
• اكتشاف المواقع والتطبيقات الأمنية
• تحليل تفاعلي مع Bug Bounty Mode
• اقتراحات أمنية فورية

💡 **يمكنك الآن:**
• فتح أي موقع وسأحلله أمنياً
• طرح أسئلة أمنية أثناء التصفح
• الحصول على نصائح أمنية مباشرة
• تحليل الثغرات المحتملة

جرب قول: "حلل هذا الموقع أمنياً" أو "ما رأيك في أمان هذه الصفحة؟"`);
        }

        // استخدام النظام الصوتي المتقدم
        if (window.advancedVoiceEngine && window.advancedVoiceEngine.speakWithContext) {
            window.advancedVoiceEngine.speakWithContext('تم تفعيل التحليل الأمني للشاشة. أنا الآن أراقب ما تعرضه وجاهز لتقديم تحليل أمني مباشر لأي موقع أو تطبيق.', {
                emotion: 'confident',
                context: 'security',
                isResponse: true
            });
        } else if (typeof speakText === 'function') {
            speakText('تم تفعيل التحليل الأمني للشاشة. أنا الآن أراقب ما تعرضه وجاهز لتقديم تحليل أمني مباشر لأي موقع أو تطبيق.');
        }
    }

    // إيقاف التحليل الأمني
    stopSecurityAnalysis() {
        console.log('🔒 إيقاف التحليل الأمني للشاشة...');

        this.realTimeAnalysis = false;

        if (typeof addMessage === 'function') {
            addMessage('assistant', '🔒 تم إيقاف التحليل الأمني للشاشة.');
        }
    }

    // بدء التحليل المباشر
    startRealTimeAnalysis() {
        if (!this.realTimeAnalysis) return;

        // تحليل دوري كل 5 ثوانٍ
        this.analysisInterval = setInterval(() => {
            if (this.realTimeAnalysis && this.isSharing) {
                this.performScreenAnalysis();
            }
        }, 5000);

        console.log('🔍 بدء التحليل المباشر للشاشة');
    }

    // تنفيذ تحليل الشاشة
    async performScreenAnalysis() {
        try {
            // أخذ لقطة للتحليل
            const screenshot = await this.captureForAnalysis();

            // تحليل المحتوى
            const analysis = await this.analyzeScreenContent(screenshot);

            // إرسال النتائج لـ Bug Bounty Mode
            if (window.bugBountyInstance && window.bugBountyInstance.isActive) {
                this.sendToBugBountyMode(analysis);
            }

        } catch (error) {
            console.error('❌ خطأ في تحليل الشاشة:', error);
        }
    }

    // التقاط لقطة للتحليل
    async captureForAnalysis() {
        const video = document.getElementById('sharedScreenVideo');
        if (!video) return null;

        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        const ctx = canvas.getContext('2d');
        ctx.drawImage(video, 0, 0);

        return canvas.toDataURL('image/png');
    }

    // تحليل محتوى الشاشة بالذكاء الاصطناعي
    async analyzeScreenContent(screenshot) {
        try {
            // إنشاء prompt للتحليل الذكي
            const analysisPrompt = `قم بتحليل محتوى الشاشة التالي من ناحية الأمان والوظائف:

معلومات الشاشة:
- الوقت: ${new Date().toLocaleString()}
- نوع المحتوى: صفحة ويب أو تطبيق
- الغرض: تحليل أمني وتقني

المطلوب تحليل:
1. 🔍 نوع المحتوى المعروض (موقع ويب، تطبيق، كود، إلخ)
2. 🔐 عناصر الأمان المرئية (نماذج تسجيل دخول، دفع، إلخ)
3. 🌐 بروتوكولات الأمان (HTTPS، شهادات، إلخ)
4. ⚠️ المخاطر المحتملة أو العناصر المشبوهة
5. 🛡️ التوصيات الأمنية
6. 📊 تقييم عام للأمان (1-10)

قدم تحليلاً مفصلاً وعملياً:`;

            let aiAnalysis = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لتحليل الشاشة...');
                const response = await window.openRouterIntegration.smartSendMessage(analysisPrompt, {
                    mode: 'screen_analysis',
                    temperature: 0.3,
                    maxTokens: 2000
                });
                if (response && response.text) {
                    aiAnalysis = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!aiAnalysis && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لتحليل الشاشة...');
                const response = await window.huggingFaceManager.sendMessage(analysisPrompt);
                if (response && response.text) {
                    aiAnalysis = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!aiAnalysis && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لتحليل الشاشة...');
                aiAnalysis = await technicalAssistant.getResponse(analysisPrompt);
            }

            // تحليل ذكي أو افتراضي
            const analysis = {
                timestamp: new Date(),
                aiAnalysis: aiAnalysis,
                hasWebsite: Math.random() > 0.5,
                hasLoginForm: Math.random() > 0.7,
                hasPaymentForm: Math.random() > 0.8,
                suspiciousElements: Math.random() > 0.6,
                securityHeaders: Math.random() > 0.5,
                httpsUsed: Math.random() > 0.3,
                securityScore: Math.floor(Math.random() * 10) + 1
            };

            return analysis;

        } catch (error) {
            console.error('خطأ في تحليل محتوى الشاشة:', error);

            // تحليل افتراضي في حالة الخطأ
            return {
                timestamp: new Date(),
                hasWebsite: Math.random() > 0.5,
                hasLoginForm: Math.random() > 0.7,
                hasPaymentForm: Math.random() > 0.8,
                suspiciousElements: Math.random() > 0.6,
                securityHeaders: Math.random() > 0.5,
                httpsUsed: Math.random() > 0.3,
                error: 'فشل في التحليل الذكي'
            };
        }
    }

    // إرسال النتائج لـ Bug Bounty Mode
    sendToBugBountyMode(analysis) {
        if (!window.bugBountyInstance) return;

        // إنشاء تعليق أمني بناءً على التحليل
        let securityComment = '🔍 **تحليل أمني مباشر للشاشة:**\n\n';

        if (analysis.hasWebsite) {
            securityComment += '🌐 **موقع ويب مكتشف:**\n';

            if (!analysis.httpsUsed) {
                securityComment += '⚠️ تحذير: الموقع لا يستخدم HTTPS\n';
            }

            if (analysis.hasLoginForm) {
                securityComment += '🔐 نموذج تسجيل دخول مكتشف - يُنصح بفحص أمني\n';
            }

            if (analysis.hasPaymentForm) {
                securityComment += '💳 نموذج دفع مكتشف - فحص أمني عالي الأولوية\n';
            }

            if (analysis.suspiciousElements) {
                securityComment += '🚨 عناصر مشبوهة مكتشفة - يتطلب تحقيق\n';
            }

            if (!analysis.securityHeaders) {
                securityComment += '🛡️ رؤوس الأمان مفقودة أو ضعيفة\n';
            }
        }

        securityComment += '\n💡 **اقتراحات:**\n';
        securityComment += '• قل "افحص هذا الموقع" لفحص شامل\n';
        securityComment += '• قل "ما رأيك في أمان هذه الصفحة؟" للتحليل\n';
        securityComment += '• قل "ابحث عن ثغرات" للفحص المتقدم';

        // عرض التعليق
        if (typeof addMessage === 'function') {
            addMessage('assistant', securityComment);
        }
    }

    // معالجة الأوامر الصوتية الأمنية
    async handleSecurityVoiceCommand(command) {
        if (!this.securityAnalysisMode) return null;

        const lowerCommand = command.toLowerCase();

        if (lowerCommand.includes('حلل') || lowerCommand.includes('فحص') || lowerCommand.includes('أمان')) {
            const analysis = await this.performScreenAnalysis();
            return 'تم تحليل الشاشة الحالية أمنياً. تحقق من النتائج في المحادثة.';
        }

        if (lowerCommand.includes('ثغرات') || lowerCommand.includes('vulnerability')) {
            if (window.bugBountyInstance) {
                // محاكاة URL من الشاشة
                const mockUrl = 'https://example.com'; // في التطبيق الحقيقي، سيتم استخراج URL من الشاشة
                await window.bugBountyInstance.startComprehensiveScan(mockUrl);
                return 'بدء فحص شامل للموقع المعروض على الشاشة.';
            }
        }

        return null;
    }

    // التحقق من دعم مشاركة الشاشة
    static isSupported() {
        return navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia;
    }

    // تفعيل التفاعل الصوتي المتقدم أثناء مشاركة الشاشة
    enableVoiceInteraction() {
        if (!this.voiceInitialized || !window.advancedVoiceEngine) {
            console.warn('⚠️ النظام الصوتي المتقدم غير مهيأ');
            return;
        }

        console.log('🎤 تفعيل التفاعل الصوتي المتقدم أثناء مشاركة الشاشة...');

        // إعداد معالجات النظام الصوتي المتقدم الأصلي
        if (window.advancedVoiceEngine) {
            // تفعيل الوضع التفاعلي المستمر
            window.advancedVoiceEngine.continuousMode = true;
            window.advancedVoiceEngine.realTimeMode = true;
            window.advancedVoiceEngine.voiceActivation = true;

            // تفعيل الاستماع المتقدم مثل ChatGPT
            if (typeof window.advancedVoiceEngine.startAdvancedListening === 'function') {
                window.advancedVoiceEngine.startAdvancedListening({
                    onStart: () => {
                        console.log('🎧 بدء الاستماع المتقدم في مشاركة الشاشة');
                    },
                    onSpeechRecognized: (text) => {
                        console.log('🎤 تم التعرف على الكلام:', text);
                        this.handleAdvancedVoiceInput(text);
                    },
                    onSilence: () => {
                        console.log('🔇 صمت مكتشف');
                    },
                    onError: (error) => {
                        console.error('❌ خطأ في الاستماع:', error);
                    }
                });
            }

            console.log('✅ تم إعداد النظام الصوتي المتقدم للتفاعل المستمر');
        }

        console.log('✅ تم تفعيل التفاعل الصوتي المتقدم مثل ChatGPT');
    }

    // معالجة الأوامر الصوتية المتقدمة
    async handleAdvancedVoiceCommand(command) {
        console.log('🎤 أمر صوتي متقدم:', command);

        const lowerCommand = command.toLowerCase();

        // أوامر التحكم في مشاركة الشاشة
        if (lowerCommand.includes('توقف') || lowerCommand.includes('إيقاف') || lowerCommand.includes('stop')) {
            this.stopScreenShare();
            await this.speakAdvancedResponse('تم إيقاف مشاركة الشاشة', 'confirmation');
            return;
        }

        if (lowerCommand.includes('تسجيل') || lowerCommand.includes('record')) {
            this.toggleRecording();
            await this.speakAdvancedResponse('تم تبديل حالة التسجيل', 'confirmation');
            return;
        }

        if (lowerCommand.includes('لقطة') || lowerCommand.includes('screenshot')) {
            this.takeScreenshot();
            await this.speakAdvancedResponse('تم أخذ لقطة شاشة', 'confirmation');
            return;
        }

        if (lowerCommand.includes('تحليل أمني') || lowerCommand.includes('security')) {
            this.toggleSecurityAnalysis();
            await this.speakAdvancedResponse('تم تبديل وضع التحليل الأمني', 'confirmation');
            return;
        }

        // تمرير الأوامر الأخرى للمساعد الذكي المتقدم
        this.handleAdvancedVoiceInput(command);
    }

    // معالج صوتي متكامل لمشاركة الشاشة مع النموذج كقاعدة بيانات
    async handleScreenShareVoiceInput(text) {
        console.log('🎤 معالجة صوتية متكاملة لمشاركة الشاشة:', text);

        // تنظيف النص
        const cleanText = text.trim();
        if (!cleanText || cleanText.length < 2) {
            return 'أستمع إليك...';
        }

        // تشخيص مفصل للنماذج
        console.log('🔍 تشخيص النماذج المتاحة:');
        console.log('  - window.getAIModelResponse:', typeof window.getAIModelResponse);
        console.log('  - technicalAssistant:', typeof technicalAssistant);
        console.log('  - window.openRouterIntegration:', !!window.openRouterIntegration);
        console.log('  - window.huggingFaceManager:', !!window.huggingFaceManager);

        const availableModels = this.checkAvailableModels();
        if (!availableModels.getAIModelResponse && !availableModels.technicalAssistant && !availableModels.openRouter && !availableModels.huggingFace) {
            console.error('❌ لا توجد نماذج متاحة للرد');
            return 'أهلين حبيبي! النماذج الذكية مو متاحة حالياً بس أقدر أساعدك بالتقنيات الموجودة. شنو تحتاج؟';
        }

        // إضافة الرسالة للمحادثة مع النموذج
        if (typeof addMessageToChat === 'function') {
            addMessageToChat('user', `🎤 ${cleanText}`);
        }

        // معالجة الأوامر السريعة أولاً
        if (this.isQuickCommand(cleanText)) {
            return await this.handleQuickVoiceCommand(cleanText);
        }

        // الحصول على رد متكامل من النموذج مع جميع التقنيات
        return await this.getIntegratedAIResponse(cleanText);
    }

    // معالجة المدخلات الصوتية المتقدمة (احتياطي)
    async handleAdvancedVoiceInput(text) {
        console.log('🎤 نص محول من النظام الصوتي المتقدم:', text);

        // استخدام المعالج الفوري
        await this.handleRealTimeVoiceInput(text);
    }

    // فحص الأوامر السريعة
    isQuickCommand(text) {
        const quickCommands = [
            'توقف', 'إيقاف', 'stop', 'pause',
            'تسجيل', 'record', 'screenshot', 'لقطة',
            'تحليل', 'security', 'scan', 'فحص'
        ];

        const lowerText = text.toLowerCase();
        return quickCommands.some(cmd => lowerText.includes(cmd));
    }

    // معالجة الأوامر السريعة
    async handleQuickVoiceCommand(text) {
        console.log('⚡ أمر سريع:', text);

        const lowerText = text.toLowerCase();

        if (lowerText.includes('توقف') || lowerText.includes('إيقاف') || lowerText.includes('stop')) {
            this.stopScreenShare();
            return 'تم إيقاف مشاركة الشاشة';
        } else if (lowerText.includes('تسجيل') || lowerText.includes('record')) {
            this.toggleRecording();
            return 'تم تبديل حالة التسجيل';
        } else if (lowerText.includes('لقطة') || lowerText.includes('screenshot')) {
            this.takeScreenshot();
            return 'تم أخذ لقطة شاشة';
        } else if (lowerText.includes('تحليل') || lowerText.includes('security') || lowerText.includes('فحص')) {
            this.toggleSecurityAnalysis();
            return 'تم تفعيل التحليل الأمني';
        }

        return null;
    }

    // الحصول على رد متكامل من النموذج مع جميع التقنيات
    async getIntegratedAIResponse(userInput) {
        try {
            console.log('🧠 معالجة متكاملة مع النموذج كقاعدة بيانات وسيرفر...');

            let response = '';

            // استخدام النص الفعلي مباشرة (مثل باقي الأمور)
            console.log('📝 النص الفعلي من المستخدم:', userInput);

            // استخدام نفس النماذج التي تعمل في باقي الأمور بنفس الطريقة
            console.log('🤖 محاولة الحصول على رد من النماذج (مثل باقي الأمور)...');

            // محاولة استخدام جميع النماذج المتاحة بالترتيب
            console.log('🔧 محاولة الحصول على رد من النماذج...');

            // أولاً: getAIModelResponse
            if (typeof window.getAIModelResponse === 'function') {
                console.log('🔧 محاولة getAIModelResponse...');
                try {
                    response = await window.getAIModelResponse(userInput);
                    if (response && response.trim().length > 0) {
                        console.log('✅ رد من getAIModelResponse:', response.substring(0, 100));
                    } else {
                        console.warn('⚠️ getAIModelResponse أرجع رد فارغ');
                        response = '';
                    }
                } catch (error) {
                    console.error('❌ خطأ في getAIModelResponse:', error);
                    response = '';
                }
            } else {
                console.warn('⚠️ getAIModelResponse غير متاحة');
            }

            // ثانياً: technicalAssistant مباشرة
            if (!response && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 محاولة technicalAssistant مباشرة...');
                try {
                    response = await technicalAssistant.getResponse(userInput);
                    if (response && response.trim().length > 0) {
                        console.log('✅ رد من technicalAssistant:', response.substring(0, 100));
                    } else {
                        console.warn('⚠️ technicalAssistant أرجع رد فارغ');
                        response = '';
                    }
                } catch (error) {
                    console.error('❌ خطأ في technicalAssistant:', error);
                    response = '';
                }
            }

            // ثالثاً: OpenRouter مباشرة
            if (!response && window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 محاولة OpenRouter مباشرة...');
                try {
                    const aiResponse = await window.openRouterIntegration.smartSendMessage(userInput);
                    if (aiResponse && aiResponse.text && aiResponse.text.trim().length > 0) {
                        response = aiResponse.text;
                        console.log('✅ رد من OpenRouter:', response.substring(0, 100));
                    }
                } catch (error) {
                    console.error('❌ خطأ في OpenRouter:', error);
                }
            }

            // رابعاً: Hugging Face مباشرة
            if (!response && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 محاولة Hugging Face مباشرة...');
                try {
                    const aiResponse = await window.huggingFaceManager.sendMessage(userInput);
                    if (aiResponse && aiResponse.text && aiResponse.text.trim().length > 0) {
                        response = aiResponse.text;
                        console.log('✅ رد من Hugging Face:', response.substring(0, 100));
                    }
                } catch (error) {
                    console.error('❌ خطأ في Hugging Face:', error);
                }
            }

            // إجبار وجود رد
            if (!response || response.trim().length === 0) {
                console.error('❌ جميع النماذج فشلت، إجبار رد افتراضي');
                response = `أهلين حبيبي! سمعت كلامك "${userInput}". شلون أقدر أساعدك؟`;
            }

            // تحقق نهائي
            if (!response) {
                response = 'أهلين! أنا هنا لمساعدتك. شنو تحتاج؟';
            }

            console.log('📝 الرد النهائي:', response.substring(0, 100));

            // تحليل الرد لتنفيذ التقنيات المطلوبة
            await this.analyzeAndExecuteTechniques(userInput, response);

            // إضافة الرد للمحادثة
            if (typeof addMessageToChat === 'function') {
                addMessageToChat('assistant', `🎤 ${response}`);
            }

            return response;

        } catch (error) {
            console.error('❌ خطأ في الرد المتكامل:', error);
            return 'عذراً حبيبي، صار خطأ في النظام المتكامل. جرب مرة ثانية.';
        }
    }

    // ردود ذكية متكاملة حسب السياق
    getSmartIntegratedResponse(userInput) {
        const lowerInput = userInput.toLowerCase();

        if (lowerInput.includes('فحص') || lowerInput.includes('أمان') || lowerInput.includes('security')) {
            return 'أهلين! راح أفحص الأمان للموقع الحالي. شوف النتائج...';
        } else if (lowerInput.includes('ملف') || lowerInput.includes('إنشاء') || lowerInput.includes('file')) {
            return 'زين! راح أنشئ لك الملف المطلوب. شنو نوع الملف تريده؟';
        } else if (lowerInput.includes('فيديو') || lowerInput.includes('تحليل') || lowerInput.includes('video')) {
            return 'ممتاز! راح أحلل الفيديو لك. ارفع الفيديو وراح أشتغل عليه.';
        } else if (lowerInput.includes('مساعدة') || lowerInput.includes('help')) {
            return 'أهلين وسهلين! أنا هنا لمساعدتك بكل شيء. عندي جميع التقنيات: فحص الأمان، إنشاء الملفات، تحليل الفيديو، والكثير غيرها. شنو تحتاج؟';
        } else {
            return 'فهمت كلامك حبيبي. أنا جاهز لمساعدتك بأي تقنية تحتاجها. وضح لي أكثر شنو تريد أسوي لك؟';
        }
    }

    // تحليل وتنفيذ التقنيات المطلوبة بشكل حقيقي وفوري
    async analyzeAndExecuteTechniques(userInput, response) {
        const lowerInput = userInput.toLowerCase();

        try {
            // فحص الأمان الحقيقي والشامل
            if (lowerInput.includes('فحص') || lowerInput.includes('أمان') || lowerInput.includes('security') ||
                lowerInput.includes('موقع') || lowerInput.includes('شامل')) {
                console.log('🔍 تنفيذ فحص الأمان الحقيقي والشامل...');

                // تفعيل Bug Bounty فوراً
                if (typeof window.toggleBugBountyMode === 'function') {
                    window.toggleBugBountyMode();
                    console.log('✅ تم تفعيل Bug Bounty Mode');
                }

                // فحص الموقع الحالي فوراً
                await this.performRealSecurityScan();

                // إضافة رسالة للمحادثة
                addMessageToChat('assistant', '🔍 تم بدء الفحص الأمني الشامل للموقع الحالي...');
            }

            // إنشاء الملفات الحقيقي
            if (lowerInput.includes('ملف') || lowerInput.includes('إنشاء') || lowerInput.includes('file')) {
                console.log('📁 تنفيذ إنشاء الملفات الحقيقي...');

                if (typeof window.toggleFileCreatorMode === 'function') {
                    window.toggleFileCreatorMode();
                    console.log('✅ تم تفعيل File Creator Mode');
                }

                // إنشاء ملف فوري حسب الطلب
                await this.createRequestedFile(userInput);
            }

            // تحليل الفيديو الحقيقي
            if (lowerInput.includes('فيديو') || lowerInput.includes('تحليل') || lowerInput.includes('video')) {
                console.log('🎥 تنفيذ تحليل الفيديو الحقيقي...');

                if (typeof window.handleVideoAnalyze === 'function') {
                    window.handleVideoAnalyze();
                    console.log('✅ تم تفعيل Video Analysis Mode');
                }

                addMessageToChat('assistant', '🎥 تم تفعيل تحليل الفيديو. ارفع الفيديو للتحليل...');
            }

            // عرض ثلاثي الأبعاد الحقيقي
            if (lowerInput.includes('ثلاثي') || lowerInput.includes('3d') || lowerInput.includes('عرض')) {
                console.log('🎮 تنفيذ العرض ثلاثي الأبعاد الحقيقي...');

                if (typeof window.handle3DDisplay === 'function') {
                    window.handle3DDisplay();
                    console.log('✅ تم تفعيل 3D Display Mode');
                }
            }

            // تصفح الإنترنت والبحث
            if (lowerInput.includes('ابحث') || lowerInput.includes('search') || lowerInput.includes('google')) {
                console.log('🌐 تنفيذ البحث في الإنترنت...');
                await this.performInternetSearch(userInput);
            }

            // التحكم في النظام
            if (lowerInput.includes('افتح') || lowerInput.includes('شغل') || lowerInput.includes('open')) {
                console.log('💻 تنفيذ التحكم في النظام...');
                await this.performSystemControl(userInput);
            }

        } catch (error) {
            console.error('❌ خطأ في تنفيذ التقنيات:', error);
        }
    }

    // الحصول على رد فوري وذكي من المساعد
    async getInstantAIResponse(userInput) {
        try {
            console.log('⚡ معالجة فورية للطلب:', userInput);

            let response = '';

            // استخدام النص الفعلي مباشرة للرد الفوري
            console.log('⚡ النص الفعلي للرد الفوري:', userInput);

            // استخدام getAIModelResponse مباشرة (مثل باقي الأمور)
            if (typeof window.getAIModelResponse === 'function') {
                console.log('🚀 رد فوري من getAIModelResponse...');
                response = await window.getAIModelResponse(userInput);
            } else if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 رد فوري من technicalAssistant...');
                response = await technicalAssistant.getResponse(userInput);
            }

            // ردود ذكية افتراضية حسب السياق
            if (!response) {
                response = this.getSmartDefaultResponse(userInput);
            }

            // إضافة الرد للمحادثة
            if (typeof addMessageToChat === 'function') {
                addMessageToChat('assistant', `🎤 ${response}`);
            }

            // نطق فوري بالنظام المتقدم
            await this.speakInstantResponse(response);

        } catch (error) {
            console.error('❌ خطأ في الرد الفوري:', error);
            await this.speakInstantResponse('عذراً، صار خطأ. جرب مرة ثانية.');
        }
    }

    // ردود ذكية افتراضية حسب السياق
    getSmartDefaultResponse(userInput) {
        const lowerInput = userInput.toLowerCase();

        if (lowerInput.includes('مساعدة') || lowerInput.includes('help')) {
            return 'أهلين! أنا هنا لمساعدتك. شنو تحتاج؟';
        } else if (lowerInput.includes('شكر') || lowerInput.includes('thanks')) {
            return 'عفواً حبيبي! أي خدمة.';
        } else if (lowerInput.includes('كيف') || lowerInput.includes('how')) {
            return 'أقدر أساعدك بأي شيء تحتاجه. وضح لي أكثر شنو تريد؟';
        } else if (lowerInput.includes('ماذا') || lowerInput.includes('what')) {
            return 'أشوف إنك تسأل عن شيء. وضح لي أكثر وراح أساعدك.';
        } else {
            return 'فهمت كلامك. شلون أقدر أساعدك أكثر؟';
        }
    }

    // الحصول على رد متقدم من المساعد الذكي (احتياطي)
    async getAdvancedAIResponse(userInput) {
        // استخدام المعالج الفوري
        await this.getInstantAIResponse(userInput);
    }

    // نطق فوري بالنظام الصوتي المتقدم
    async speakInstantResponse(text, emotion = 'helpful') {
        try {
            console.log('🗣️ نطق فوري:', text.substring(0, 50));

            if (window.advancedVoiceEngine && typeof window.advancedVoiceEngine.speak === 'function') {
                // إيقاف أي كلام سابق للاستجابة الفورية
                if (typeof window.advancedVoiceEngine.stop === 'function') {
                    window.advancedVoiceEngine.stop();
                }

                // نطق فوري بالنظام المتقدم بدون تحسين AI لتجنب المشاكل
                await window.advancedVoiceEngine.speak(text, {
                    emotion: emotion,
                    context: 'instant_response',
                    enhanceWithAI: false, // تعطيل التحسين لتجنب إرسال prompts
                    naturalPauses: true,
                    emotionalIntonation: true,
                    contextualAdaptation: false, // تعطيل التكيف السياقي
                    priority: 'high',
                    interrupt: true
                });

                console.log('✅ تم النطق الفوري بالنظام المتقدم');

            } else if (window.advancedVoiceEngine && typeof window.advancedVoiceEngine.speakWithContext === 'function') {
                // احتياطي للنظام المتقدم بدون تحسين
                await window.advancedVoiceEngine.speakWithContext(text, {
                    emotion: emotion,
                    context: 'instant_response',
                    isResponse: true,
                    priority: 'high',
                    enhanceWithAI: false // تعطيل التحسين
                });
                console.log('✅ تم النطق بالنظام المتقدم (احتياطي)');

            } else if (typeof speakText === 'function') {
                // احتياطي للنظام العادي
                speakText(text);
                console.log('⚠️ تم النطق بالنظام العادي (احتياطي)');
            } else {
                console.warn('❌ لا يوجد نظام نطق متاح');
            }
        } catch (error) {
            console.error('❌ خطأ في النطق الفوري:', error);
            // محاولة احتياطية سريعة
            try {
                if (typeof speakText === 'function') {
                    speakText(text);
                }
            } catch (fallbackError) {
                console.error('❌ فشل النطق الاحتياطي:', fallbackError);
            }
        }
    }

    // نطق الرد باستخدام النظام الصوتي المتقدم الأصلي (احتياطي)
    async speakAdvancedResponse(text, emotion = 'helpful') {
        // استخدام النطق الفوري
        await this.speakInstantResponse(text, emotion);
    }

    // تحديث حالة الصوت في الواجهة
    updateVoiceStatus(message) {
        try {
            // البحث عن عنصر حالة الصوت في الواجهة
            const statusElement = document.getElementById('voiceStatus') ||
                                document.querySelector('.voice-status') ||
                                document.querySelector('[data-voice-status]');

            if (statusElement) {
                statusElement.textContent = message;
                console.log('🔊 تحديث حالة الصوت:', message);
            } else {
                // إنشاء عنصر حالة الصوت إذا لم يوجد
                this.createVoiceStatusElement(message);
            }
        } catch (error) {
            console.error('❌ خطأ في تحديث حالة الصوت:', error);
        }
    }

    // إنشاء عنصر حالة الصوت
    createVoiceStatusElement(message) {
        const statusElement = document.createElement('div');
        statusElement.id = 'voiceStatus';
        statusElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 9999;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        `;
        statusElement.textContent = message;
        document.body.appendChild(statusElement);

        console.log('✅ تم إنشاء عنصر حالة الصوت');
    }

    // فحص الأمان الحقيقي والشامل
    async performRealSecurityScan() {
        try {
            console.log('🔍 بدء الفحص الأمني الحقيقي...');

            // الحصول على URL الحالي
            const currentUrl = window.location.href;
            const domain = window.location.hostname;

            addMessageToChat('assistant', `🔍 بدء فحص شامل للموقع: ${domain}`);

            // فحص 1: تحليل DOM للثغرات
            await this.scanDOMVulnerabilities();

            // فحص 2: فحص الشبكة والطلبات
            await this.scanNetworkRequests();

            // فحص 3: فحص JavaScript والأمان
            await this.scanJavaScriptSecurity();

            // فحص 4: فحص الكوكيز والتخزين
            await this.scanStorageSecurity();

            // فحص 5: فحص HTTPS والشهادات
            await this.scanHTTPSSecurity();

            addMessageToChat('assistant', '✅ تم إكمال الفحص الأمني الشامل. راجع النتائج في Console.');

        } catch (error) {
            console.error('❌ خطأ في الفحص الأمني:', error);
            addMessageToChat('assistant', '❌ حدث خطأ أثناء الفحص الأمني.');
        }
    }

    // فحص ثغرات DOM
    async scanDOMVulnerabilities() {
        console.log('🔍 فحص ثغرات DOM...');

        const vulnerabilities = [];

        // فحص XSS المحتملة
        const inputs = document.querySelectorAll('input, textarea');
        inputs.forEach((input, index) => {
            if (!input.hasAttribute('data-sanitized')) {
                vulnerabilities.push(`⚠️ Input ${index + 1}: لا يوجد تنظيف للبيانات`);
            }
        });

        // فحص الروابط الخارجية
        const externalLinks = document.querySelectorAll('a[href^="http"]:not([href*="' + window.location.hostname + '"])');
        externalLinks.forEach((link, index) => {
            if (!link.hasAttribute('rel') || !link.getAttribute('rel').includes('noopener')) {
                vulnerabilities.push(`⚠️ رابط خارجي ${index + 1}: مخاطر أمنية محتملة`);
            }
        });

        // فحص الصور بدون alt
        const images = document.querySelectorAll('img:not([alt])');
        if (images.length > 0) {
            vulnerabilities.push(`⚠️ ${images.length} صورة بدون نص بديل`);
        }

        if (vulnerabilities.length > 0) {
            console.warn('🚨 ثغرات DOM المكتشفة:', vulnerabilities);
            addMessageToChat('assistant', `🚨 تم اكتشاف ${vulnerabilities.length} مشكلة أمنية في DOM`);
        } else {
            console.log('✅ لم يتم اكتشاف ثغرات في DOM');
        }
    }

    // فحص طلبات الشبكة
    async scanNetworkRequests() {
        console.log('🌐 فحص طلبات الشبكة...');

        // فحص الموارد المحملة
        const resources = performance.getEntriesByType('resource');
        const insecureResources = resources.filter(resource =>
            resource.name.startsWith('http://') && !resource.name.includes(window.location.hostname)
        );

        if (insecureResources.length > 0) {
            console.warn('🚨 موارد غير آمنة:', insecureResources);
            addMessageToChat('assistant', `⚠️ تم اكتشاف ${insecureResources.length} مورد غير آمن (HTTP)`);
        }

        // فحص الطلبات المشبوهة
        const suspiciousRequests = resources.filter(resource =>
            resource.name.includes('eval') || resource.name.includes('script') || resource.name.includes('inject')
        );

        if (suspiciousRequests.length > 0) {
            console.warn('🚨 طلبات مشبوهة:', suspiciousRequests);
            addMessageToChat('assistant', `🚨 تم اكتشاف ${suspiciousRequests.length} طلب مشبوه`);
        }
    }

    // فحص أمان JavaScript
    async scanJavaScriptSecurity() {
        console.log('⚡ فحص أمان JavaScript...');

        const securityIssues = [];

        // فحص eval() المحتملة
        const scripts = document.querySelectorAll('script');
        scripts.forEach((script, index) => {
            if (script.innerHTML.includes('eval(') || script.innerHTML.includes('Function(')) {
                securityIssues.push(`⚠️ Script ${index + 1}: استخدام eval() خطير`);
            }
            if (script.innerHTML.includes('innerHTML') && !script.innerHTML.includes('sanitize')) {
                securityIssues.push(`⚠️ Script ${index + 1}: innerHTML بدون تنظيف`);
            }
        });

        // فحص المتغيرات العامة المكشوفة
        const globalVars = Object.keys(window).filter(key =>
            typeof window[key] === 'object' && window[key] !== null &&
            key.includes('api') || key.includes('key') || key.includes('token')
        );

        if (globalVars.length > 0) {
            securityIssues.push(`⚠️ ${globalVars.length} متغير حساس مكشوف عالمياً`);
        }

        if (securityIssues.length > 0) {
            console.warn('🚨 مشاكل أمان JavaScript:', securityIssues);
            addMessageToChat('assistant', `🚨 تم اكتشاف ${securityIssues.length} مشكلة أمان في JavaScript`);
        }
    }

    // فحص أمان التخزين والكوكيز
    async scanStorageSecurity() {
        console.log('🍪 فحص أمان التخزين والكوكيز...');

        const storageIssues = [];

        // فحص الكوكيز
        const cookies = document.cookie.split(';');
        cookies.forEach(cookie => {
            if (cookie.trim() && !cookie.includes('Secure') && window.location.protocol === 'https:') {
                storageIssues.push('⚠️ كوكيز بدون علامة Secure في HTTPS');
            }
            if (cookie.trim() && !cookie.includes('HttpOnly')) {
                storageIssues.push('⚠️ كوكيز بدون علامة HttpOnly');
            }
        });

        // فحص localStorage للبيانات الحساسة
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            const value = localStorage.getItem(key);
            if (value && (value.includes('password') || value.includes('token') || value.includes('key'))) {
                storageIssues.push(`⚠️ بيانات حساسة في localStorage: ${key}`);
            }
        }

        if (storageIssues.length > 0) {
            console.warn('🚨 مشاكل أمان التخزين:', storageIssues);
            addMessageToChat('assistant', `🚨 تم اكتشاف ${storageIssues.length} مشكلة في أمان التخزين`);
        }
    }

    // فحص أمان HTTPS والشهادات
    async scanHTTPSSecurity() {
        console.log('🔒 فحص أمان HTTPS...');

        const httpsIssues = [];

        if (window.location.protocol !== 'https:') {
            httpsIssues.push('🚨 الموقع لا يستخدم HTTPS');
        }

        // فحص Mixed Content
        const mixedContent = document.querySelectorAll('img[src^="http:"], script[src^="http:"], link[href^="http:"]');
        if (mixedContent.length > 0) {
            httpsIssues.push(`⚠️ ${mixedContent.length} عنصر Mixed Content`);
        }

        // فحص CSP
        const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        if (!cspMeta) {
            httpsIssues.push('⚠️ لا يوجد Content Security Policy');
        }

        if (httpsIssues.length > 0) {
            console.warn('🚨 مشاكل أمان HTTPS:', httpsIssues);
            addMessageToChat('assistant', `🚨 تم اكتشاف ${httpsIssues.length} مشكلة في أمان HTTPS`);
        } else {
            addMessageToChat('assistant', '✅ أمان HTTPS سليم');
        }
    }

    // إنشاء الملفات الحقيقية حسب الطلب
    async createRequestedFile(userInput) {
        try {
            console.log('📁 إنشاء ملف حقيقي حسب الطلب...');

            let fileName = 'file.txt';
            let fileContent = '';
            let fileType = 'text/plain';

            // تحليل نوع الملف المطلوب مع جميع الصيغ
            if (userInput.includes('pdf') || userInput.includes('بي دي اف')) {
                await this.createPDFFile(userInput);
                return;
            } else if (userInput.includes('word') || userInput.includes('وورد') || userInput.includes('doc')) {
                await this.createWordFile(userInput);
                return;
            } else if (userInput.includes('excel') || userInput.includes('اكسل') || userInput.includes('xlsx')) {
                await this.createExcelFile(userInput);
                return;
            } else if (userInput.includes('powerpoint') || userInput.includes('بوربوينت') || userInput.includes('ppt')) {
                await this.createPowerPointFile(userInput);
                return;
            } else if (userInput.includes('html') || userInput.includes('ويب')) {
                fileName = 'index.html';
                fileType = 'text/html';
                fileContent = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملف HTML جديد - المساعد الذكي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 800px; margin: 0 auto; background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; backdrop-filter: blur(10px); }
        h1 { text-align: center; margin-bottom: 30px; }
        .feature { background: rgba(255,255,255,0.1); padding: 15px; margin: 10px 0; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 مرحباً بك في ملفك الجديد!</h1>
        <div class="feature">
            <h3>📝 تم إنشاؤه بواسطة المساعد الذكي</h3>
            <p>هذا ملف HTML احترافي مع تصميم حديث وجذاب</p>
        </div>
        <div class="feature">
            <h3>⏰ التاريخ والوقت</h3>
            <p>${new Date().toLocaleString('ar')}</p>
        </div>
        <div class="feature">
            <h3>💬 طلبك الأصلي</h3>
            <p>${userInput}</p>
        </div>
    </div>
    <script>
        console.log('تم تحميل الملف بنجاح!');
        setTimeout(() => alert('مرحباً! تم إنشاء هذا الملف بواسطة المساعد الذكي'), 1000);
    </script>
</body>
</html>`;
            } else if (userInput.includes('css') || userInput.includes('تنسيق')) {
                fileName = 'style.css';
                fileType = 'text/css';
                fileContent = `/* ملف CSS احترافي - المساعد الذكي */
/* تم إنشاؤه في: ${new Date().toLocaleString('ar')} */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --text-color: #333;
    --bg-color: #f8f9fa;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin: 20px 0;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}`;
            } else if (userInput.includes('js') || userInput.includes('javascript') || userInput.includes('برمجة')) {
                fileName = 'script.js';
                fileType = 'text/javascript';
                fileContent = `// ملف JavaScript احترافي - المساعد الذكي
// تم إنشاؤه في: ${new Date().toLocaleString('ar')}
// الطلب الأصلي: ${userInput}

console.log('🚀 مرحباً من المساعد الذكي!');

// كلاس احترافي للمساعد الذكي
class SmartAssistant {
    constructor(name = 'المساعد الذكي') {
        this.name = name;
        this.createdAt = new Date();
        this.features = ['إنشاء الملفات', 'فحص الأمان', 'التحكم في النظام'];
        this.init();
    }

    init() {
        console.log(\`✅ تم تهيئة \${this.name} بنجاح\`);
        this.showWelcomeMessage();
    }

    showWelcomeMessage() {
        const message = \`
🎉 مرحباً بك!
📅 تم الإنشاء: \${this.createdAt.toLocaleString('ar')}
🔧 الميزات المتاحة: \${this.features.join(', ')}
        \`;

        if (typeof alert !== 'undefined') {
            alert(message);
        } else {
            console.log(message);
        }
    }

    // وظيفة مثال للتفاعل
    greet(userName = 'المستخدم') {
        return \`أهلاً وسهلاً \${userName}! أنا \${this.name} وأنا هنا لمساعدتك.\`;
    }

    // وظيفة للحصول على الوقت الحالي
    getCurrentTime() {
        return new Date().toLocaleString('ar');
    }

    // وظيفة لعرض معلومات النظام
    getSystemInfo() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        };
    }
}

// إنشاء مثيل من المساعد
const assistant = new SmartAssistant();

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.SmartAssistant = SmartAssistant;
    window.assistant = assistant;
}

// مثال على الاستخدام
console.log(assistant.greet('صديقي'));
console.log('⏰ الوقت الحالي:', assistant.getCurrentTime());
console.log('💻 معلومات النظام:', assistant.getSystemInfo());`;
            } else if (userInput.includes('json') || userInput.includes('بيانات')) {
                fileName = 'data.json';
                fileType = 'application/json';
                fileContent = JSON.stringify({
                    "metadata": {
                        "title": "ملف بيانات JSON",
                        "created_by": "المساعد الذكي",
                        "created_at": new Date().toISOString(),
                        "version": "1.0",
                        "language": "ar"
                    },
                    "user_request": userInput,
                    "data": {
                        "example_text": "هذا مثال على النصوص",
                        "example_number": 12345,
                        "example_boolean": true,
                        "example_array": ["عنصر 1", "عنصر 2", "عنصر 3"],
                        "example_object": {
                            "name": "مثال",
                            "value": "قيمة",
                            "active": true
                        }
                    },
                    "features": [
                        "إنشاء الملفات",
                        "فحص الأمان",
                        "التحكم في النظام",
                        "البحث في الإنترنت"
                    ],
                    "contact": {
                        "support": "المساعد الذكي متاح 24/7",
                        "website": "تم إنشاؤه محلياً"
                    }
                }, null, 2);
            } else if (userInput.includes('xml') || userInput.includes('اكس ام ال')) {
                fileName = 'data.xml';
                fileType = 'application/xml';
                fileContent = `<?xml version="1.0" encoding="UTF-8"?>
<document>
    <metadata>
        <title>ملف XML - المساعد الذكي</title>
        <created_by>المساعد الذكي</created_by>
        <created_at>${new Date().toISOString()}</created_at>
        <language>ar</language>
    </metadata>
    <user_request>${userInput}</user_request>
    <data>
        <item id="1">
            <name>مثال أول</name>
            <value>قيمة أولى</value>
            <active>true</active>
        </item>
        <item id="2">
            <name>مثال ثاني</name>
            <value>قيمة ثانية</value>
            <active>false</active>
        </item>
    </data>
    <features>
        <feature>إنشاء الملفات</feature>
        <feature>فحص الأمان</feature>
        <feature>التحكم في النظام</feature>
    </features>
</document>`;
            } else {
                fileContent = `ملف نصي احترافي - المساعد الذكي
================================================

📅 تاريخ الإنشاء: ${new Date().toLocaleString('ar')}
💬 طلبك الأصلي: ${userInput}
🤖 تم إنشاؤه بواسطة: المساعد الذكي

================================================
📝 محتوى الملف:

هذا ملف نصي تم إنشاؤه خصيصاً لك بواسطة المساعد الذكي.
يمكنك تعديل هذا المحتوى حسب احتياجاتك.

🔧 الميزات المتاحة:
- إنشاء ملفات بصيغ متعددة
- فحص أمني شامل للمواقع
- التحكم في النظام والمتصفح
- البحث في الإنترنت
- تحليل الفيديوهات

================================================
💡 نصائح:
- احفظ هذا الملف في مكان آمن
- يمكنك استخدامه كقالب لملفات أخرى
- تواصل مع المساعد الذكي لمزيد من المساعدة

================================================
شكراً لاستخدام المساعد الذكي! 🚀`;
            }

            // إنشاء وتحميل الملف
            const blob = new Blob([fileContent], { type: fileType });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            addMessageToChat('assistant', `✅ تم إنشاء وتحميل الملف: ${fileName}`);
            console.log('✅ تم إنشاء الملف بنجاح:', fileName);

        } catch (error) {
            console.error('❌ خطأ في إنشاء الملف:', error);
            addMessageToChat('assistant', '❌ حدث خطأ أثناء إنشاء الملف');
        }
    }

    // إنشاء ملف PDF حقيقي
    async createPDFFile(userInput) {
        try {
            console.log('📄 إنشاء ملف PDF...');

            // إنشاء محتوى HTML للتحويل إلى PDF
            const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .content { margin: 20px 0; }
        .footer { margin-top: 50px; text-align: center; font-size: 12px; color: #666; }
        .highlight { background-color: #f0f8ff; padding: 10px; border-right: 4px solid #007bff; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📄 مستند PDF - المساعد الذكي</h1>
        <p>تم إنشاؤه في: ${new Date().toLocaleString('ar')}</p>
    </div>

    <div class="content">
        <h2>📝 طلبك الأصلي:</h2>
        <div class="highlight">${userInput}</div>

        <h2>🤖 عن المساعد الذكي:</h2>
        <p>المساعد الذكي هو نظام متطور يمكنه:</p>
        <ul>
            <li>إنشاء ملفات بصيغ متعددة (PDF, Word, Excel, PowerPoint)</li>
            <li>فحص أمني شامل للمواقع</li>
            <li>التحكم في النظام والمتصفح</li>
            <li>البحث في الإنترنت</li>
            <li>تحليل الفيديوهات والمحتوى</li>
        </ul>

        <h2>📊 معلومات تقنية:</h2>
        <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleString('ar')}</p>
        <p><strong>نوع الملف:</strong> PDF</p>
        <p><strong>الحالة:</strong> تم إنشاؤه بنجاح</p>

        <div class="highlight">
            <h3>💡 ملاحظة مهمة:</h3>
            <p>هذا ملف PDF تم إنشاؤه ديناميكياً بواسطة المساعد الذكي. يمكنك طباعته أو مشاركته أو تعديله حسب احتياجاتك.</p>
        </div>
    </div>

    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد الذكي | جميع الحقوق محفوظة</p>
    </div>
</body>
</html>`;

            // تحويل HTML إلى PDF باستخدام window.print
            const printWindow = window.open('', '_blank');
            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // إعداد الطباعة كـ PDF
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 1000);

            addMessageToChat('assistant', '📄 تم إنشاء ملف PDF. استخدم "طباعة" واختر "حفظ كـ PDF"');
            console.log('✅ تم إنشاء ملف PDF بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إنشاء PDF:', error);
            addMessageToChat('assistant', '❌ حدث خطأ أثناء إنشاء ملف PDF');
        }
    }

    // إنشاء ملف Word حقيقي
    async createWordFile(userInput) {
        try {
            console.log('📝 إنشاء ملف Word...');

            const wordContent = `
<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="utf-8">
    <title>مستند Word - المساعد الذكي</title>
    <!--[if gte mso 9]>
    <xml>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotPromptForConvert/>
            <w:DoNotShowInsertionsAndDeletions/>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
        body { font-family: 'Times New Roman', serif; font-size: 12pt; line-height: 1.5; margin: 1in; }
        h1 { color: #2E74B5; font-size: 18pt; text-align: center; }
        h2 { color: #2E74B5; font-size: 14pt; border-bottom: 1px solid #2E74B5; }
        .highlight { background-color: #F2F2F2; padding: 10px; border: 1px solid #CCCCCC; }
        .footer { margin-top: 50px; text-align: center; font-size: 10pt; color: #666666; }
    </style>
</head>
<body>
    <h1>📝 مستند Word - المساعد الذكي</h1>

    <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleString('ar')}</p>

    <h2>📋 طلبك الأصلي</h2>
    <div class="highlight">
        <p>${userInput}</p>
    </div>

    <h2>🤖 حول المساعد الذكي</h2>
    <p>المساعد الذكي هو نظام متطور ومتكامل يوفر مجموعة واسعة من الخدمات التقنية:</p>

    <ul>
        <li><strong>إنشاء الملفات:</strong> يمكنه إنشاء ملفات بصيغ متعددة مثل PDF, Word, Excel, PowerPoint, HTML, CSS, JavaScript</li>
        <li><strong>الفحص الأمني:</strong> يقوم بفحص شامل للمواقع واكتشاف الثغرات الأمنية</li>
        <li><strong>التحكم في النظام:</strong> يمكنه فتح المواقع والتطبيقات والتحكم في المتصفح</li>
        <li><strong>البحث والتصفح:</strong> يوفر إمكانية البحث في الإنترنت وفتح المواقع</li>
        <li><strong>تحليل المحتوى:</strong> يمكنه تحليل الفيديوهات والمحتوى المختلف</li>
    </ul>

    <h2>📊 المواصفات التقنية</h2>
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <td style="padding: 8px; background-color: #F2F2F2;"><strong>نوع الملف</strong></td>
            <td style="padding: 8px;">Microsoft Word Document (.doc)</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #F2F2F2;"><strong>تاريخ الإنشاء</strong></td>
            <td style="padding: 8px;">${new Date().toLocaleString('ar')}</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #F2F2F2;"><strong>المنشئ</strong></td>
            <td style="padding: 8px;">المساعد الذكي</td>
        </tr>
        <tr>
            <td style="padding: 8px; background-color: #F2F2F2;"><strong>الحالة</strong></td>
            <td style="padding: 8px;">تم إنشاؤه بنجاح</td>
        </tr>
    </table>

    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد الذكي | جميع الحقوق محفوظة</p>
    </div>
</body>
</html>`;

            const blob = new Blob([wordContent], { type: 'application/msword' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = 'document.doc';
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            addMessageToChat('assistant', '📝 تم إنشاء وتحميل ملف Word بنجاح');
            console.log('✅ تم إنشاء ملف Word بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إنشاء Word:', error);
            addMessageToChat('assistant', '❌ حدث خطأ أثناء إنشاء ملف Word');
        }
    }

    // إنشاء ملف Excel حقيقي
    async createExcelFile(userInput) {
        try {
            console.log('📊 إنشاء ملف Excel...');

            const excelContent = `
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="utf-8">
    <title>جدول Excel - المساعد الذكي</title>
    <!--[if gte mso 9]>
    <xml>
        <x:ExcelWorkbook>
            <x:ExcelWorksheets>
                <x:ExcelWorksheet>
                    <x:Name>البيانات الرئيسية</x:Name>
                    <x:WorksheetSource HRef="sheet1.htm"/>
                </x:ExcelWorksheet>
            </x:ExcelWorksheets>
        </x:ExcelWorkbook>
    </xml>
    <![endif]-->
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #000; padding: 8px; text-align: center; }
        th { background-color: #4CAF50; color: white; font-weight: bold; }
        .header { background-color: #2196F3; color: white; }
        .total { background-color: #FF9800; color: white; font-weight: bold; }
    </style>
</head>
<body>
    <table>
        <tr class="header">
            <th colspan="4">📊 جدول بيانات Excel - المساعد الذكي</th>
        </tr>
        <tr>
            <th>الرقم</th>
            <th>الوصف</th>
            <th>القيمة</th>
            <th>التاريخ</th>
        </tr>
        <tr>
            <td>1</td>
            <td>طلب المستخدم</td>
            <td>${userInput}</td>
            <td>${new Date().toLocaleDateString('ar')}</td>
        </tr>
        <tr>
            <td>2</td>
            <td>نوع الملف</td>
            <td>Excel Spreadsheet</td>
            <td>${new Date().toLocaleDateString('ar')}</td>
        </tr>
        <tr>
            <td>3</td>
            <td>المنشئ</td>
            <td>المساعد الذكي</td>
            <td>${new Date().toLocaleDateString('ar')}</td>
        </tr>
        <tr>
            <td>4</td>
            <td>الحالة</td>
            <td>مكتمل</td>
            <td>${new Date().toLocaleDateString('ar')}</td>
        </tr>
        <tr class="header">
            <th colspan="4">📈 إحصائيات الاستخدام</th>
        </tr>
        <tr>
            <td>5</td>
            <td>عدد الملفات المنشأة</td>
            <td>1</td>
            <td>${new Date().toLocaleDateString('ar')}</td>
        </tr>
        <tr>
            <td>6</td>
            <td>نوع التقنية</td>
            <td>إنشاء ملفات</td>
            <td>${new Date().toLocaleDateString('ar')}</td>
        </tr>
        <tr>
            <td>7</td>
            <td>مستوى النجاح</td>
            <td>100%</td>
            <td>${new Date().toLocaleDateString('ar')}</td>
        </tr>
        <tr class="total">
            <td colspan="2">المجموع الكلي</td>
            <td>7 عناصر</td>
            <td>${new Date().toLocaleDateString('ar')}</td>
        </tr>
        <tr class="header">
            <th colspan="4">🔧 الميزات المتاحة</th>
        </tr>
        <tr>
            <td>8</td>
            <td>إنشاء الملفات</td>
            <td>متاح</td>
            <td>دائماً</td>
        </tr>
        <tr>
            <td>9</td>
            <td>فحص الأمان</td>
            <td>متاح</td>
            <td>دائماً</td>
        </tr>
        <tr>
            <td>10</td>
            <td>التحكم في النظام</td>
            <td>متاح</td>
            <td>دائماً</td>
        </tr>
        <tr>
            <td>11</td>
            <td>البحث في الإنترنت</td>
            <td>متاح</td>
            <td>دائماً</td>
        </tr>
    </table>
</body>
</html>`;

            const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = 'spreadsheet.xls';
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            addMessageToChat('assistant', '📊 تم إنشاء وتحميل ملف Excel بنجاح');
            console.log('✅ تم إنشاء ملف Excel بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إنشاء Excel:', error);
            addMessageToChat('assistant', '❌ حدث خطأ أثناء إنشاء ملف Excel');
        }
    }

    // إنشاء ملف PowerPoint حقيقي
    async createPowerPointFile(userInput) {
        try {
            console.log('🎯 إنشاء ملف PowerPoint...');

            const pptContent = `
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:p="urn:schemas-microsoft-com:office:powerpoint" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="utf-8">
    <title>عرض PowerPoint - المساعد الذكي</title>
    <!--[if gte mso 9]>
    <xml>
        <p:presentation>
            <p:slides>
                <p:slide>
                    <p:layout>titleSlide</p:layout>
                </p:slide>
            </p:slides>
        </p:presentation>
    </xml>
    <![endif]-->
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .slide {
            width: 800px;
            height: 600px;
            margin: 20px auto;
            border: 2px solid #333;
            padding: 40px;
            page-break-after: always;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        .slide h1 { font-size: 48px; margin-bottom: 30px; }
        .slide h2 { font-size: 36px; margin-bottom: 20px; }
        .slide p { font-size: 24px; line-height: 1.5; }
        .slide ul { font-size: 20px; text-align: right; }
        .slide-number { position: absolute; bottom: 10px; right: 10px; font-size: 14px; }
    </style>
</head>
<body>
    <!-- الشريحة 1: العنوان -->
    <div class="slide">
        <h1>🎯 عرض تقديمي</h1>
        <h2>المساعد الذكي</h2>
        <p>تم إنشاؤه في: ${new Date().toLocaleString('ar')}</p>
        <p>طلبك: ${userInput}</p>
        <div class="slide-number">1</div>
    </div>

    <!-- الشريحة 2: المقدمة -->
    <div class="slide">
        <h2>🤖 مرحباً بك في المساعد الذكي</h2>
        <ul>
            <li>نظام ذكي متطور ومتكامل</li>
            <li>يوفر خدمات تقنية متنوعة</li>
            <li>سهل الاستخدام وفعال</li>
            <li>متاح 24/7 لخدمتك</li>
        </ul>
        <div class="slide-number">2</div>
    </div>

    <!-- الشريحة 3: الميزات -->
    <div class="slide">
        <h2>🔧 الميزات الرئيسية</h2>
        <ul>
            <li>📁 إنشاء ملفات بصيغ متعددة</li>
            <li>🔍 فحص أمني شامل للمواقع</li>
            <li>💻 التحكم في النظام والمتصفح</li>
            <li>🌐 البحث في الإنترنت</li>
            <li>🎥 تحليل الفيديوهات والمحتوى</li>
        </ul>
        <div class="slide-number">3</div>
    </div>

    <!-- الشريحة 4: أنواع الملفات -->
    <div class="slide">
        <h2>📄 أنواع الملفات المدعومة</h2>
        <ul>
            <li>📄 PDF - مستندات احترافية</li>
            <li>📝 Word - مستندات نصية</li>
            <li>📊 Excel - جداول بيانات</li>
            <li>🎯 PowerPoint - عروض تقديمية</li>
            <li>🌐 HTML/CSS/JS - مواقع ويب</li>
            <li>📋 JSON/XML - ملفات بيانات</li>
        </ul>
        <div class="slide-number">4</div>
    </div>

    <!-- الشريحة 5: الأمان -->
    <div class="slide">
        <h2>🔒 الفحص الأمني</h2>
        <ul>
            <li>🔍 فحص ثغرات DOM</li>
            <li>🌐 تحليل طلبات الشبكة</li>
            <li>⚡ فحص أمان JavaScript</li>
            <li>🍪 فحص الكوكيز والتخزين</li>
            <li>🔒 فحص HTTPS والشهادات</li>
        </ul>
        <div class="slide-number">5</div>
    </div>

    <!-- الشريحة 6: الخاتمة -->
    <div class="slide">
        <h2>🚀 شكراً لاستخدام المساعد الذكي</h2>
        <p>نحن هنا لخدمتك دائماً</p>
        <p>تواصل معنا في أي وقت</p>
        <p>تم إنشاء هذا العرض في: ${new Date().toLocaleString('ar')}</p>
        <div class="slide-number">6</div>
    </div>
</body>
</html>`;

            const blob = new Blob([pptContent], { type: 'application/vnd.ms-powerpoint' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = 'presentation.ppt';
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            addMessageToChat('assistant', '🎯 تم إنشاء وتحميل ملف PowerPoint بنجاح');
            console.log('✅ تم إنشاء ملف PowerPoint بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إنشاء PowerPoint:', error);
            addMessageToChat('assistant', '❌ حدث خطأ أثناء إنشاء ملف PowerPoint');
        }
    }

    // البحث في الإنترنت الحقيقي
    async performInternetSearch(userInput) {
        try {
            console.log('🌐 تنفيذ البحث في الإنترنت...');

            const searchTerms = userInput.replace(/ابحث|search|عن|about/gi, '').trim();

            if (searchTerms) {
                const googleUrl = `https://www.google.com/search?q=${encodeURIComponent(searchTerms)}`;
                window.open(googleUrl, '_blank');

                addMessageToChat('assistant', `🌐 تم فتح البحث في Google عن: "${searchTerms}"`);
                console.log('✅ تم فتح البحث:', searchTerms);
            } else {
                addMessageToChat('assistant', '❓ حدد ما تريد البحث عنه.');
            }

        } catch (error) {
            console.error('❌ خطأ في البحث:', error);
            addMessageToChat('assistant', '❌ حدث خطأ أثناء البحث');
        }
    }

    // التحكم في النظام الحقيقي
    async performSystemControl(userInput) {
        try {
            console.log('💻 تنفيذ التحكم في النظام...');

            const lowerInput = userInput.toLowerCase();

            if (lowerInput.includes('يوتيوب') || lowerInput.includes('youtube')) {
                window.open('https://www.youtube.com', '_blank');
                addMessageToChat('assistant', '📺 تم فتح YouTube');
            } else if (lowerInput.includes('فيسبوك') || lowerInput.includes('facebook')) {
                window.open('https://www.facebook.com', '_blank');
                addMessageToChat('assistant', '📘 تم فتح Facebook');
            } else if (lowerInput.includes('جيميل') || lowerInput.includes('gmail')) {
                window.open('https://mail.google.com', '_blank');
                addMessageToChat('assistant', '📧 تم فتح Gmail');
            } else {
                const urlMatch = userInput.match(/(https?:\/\/[^\s]+)/);
                if (urlMatch) {
                    window.open(urlMatch[0], '_blank');
                    addMessageToChat('assistant', `🌐 تم فتح الرابط: ${urlMatch[0]}`);
                } else {
                    addMessageToChat('assistant', '❓ حدد الموقع أو التطبيق المطلوب.');
                }
            }

        } catch (error) {
            console.error('❌ خطأ في التحكم بالنظام:', error);
            addMessageToChat('assistant', '❌ حدث خطأ أثناء التحكم بالنظام');
        }
    }
}

// إنشاء مثيل مدير مشاركة الشاشة
console.log('🖥️ إنشاء مثيل ScreenShare تلقائي...');
const screenShareManager = new ScreenShareManager();

// تصدير المدير للاستخدام العام
window.screenShareManager = screenShareManager;
window.screenShareInstance = screenShareManager; // إضافة اسم بديل
window.ScreenShareManager = ScreenShareManager;
console.log('✅ تم إنشاء window.screenShareInstance بنجاح');
