/**
 * API Integration Manager
 * Manages connections to external AI models via API
 */

class APIManager {
    constructor() {
        this.isEnabled = false;
        this.currentProvider = null;
        this.apiConfigs = this.loadAPIConfigs();
        this.supportedProviders = this.initSupportedProviders();
        this.requestHistory = [];
        
        this.init();
    }

    // Initialize the API manager
    init() {
        console.log('🔌 تهيئة مدير API للنماذج الخارجية...');
        
        // Load saved settings
        this.loadSettings();
        
        // Check API availability
        this.checkAPIAvailability();
        
        console.log('✅ تم تهيئة مدير API');
    }

    // Initialize supported providers
    initSupportedProviders() {
        return {
            openai: {
                name: 'OpenAI GPT',
                baseUrl: 'https://api.openai.com/v1',
                models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4o', 'gpt-4o-mini'],
                requiresKey: true,
                type: 'chat',
                hasVoice: true,
                voiceModels: ['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer'],
                ttsEndpoint: 'https://api.openai.com/v1/audio/speech'
            },
            gemini: {
                name: 'Google Gemini',
                baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                models: ['gemini-pro', 'gemini-pro-vision', 'gemini-1.5-pro', 'gemini-1.5-flash'],
                requiresKey: true,
                type: 'generate',
                hasVoice: true,
                voiceModels: ['gemini-voice-1', 'gemini-voice-2']
            },
            'gemini-studio': {
                name: 'Gemini Studio',
                baseUrl: 'https://aistudio.google.com/app/apikey',
                models: ['gemini-1.5-pro-latest', 'gemini-1.5-flash-latest', 'gemini-exp-1114'],
                requiresKey: true,
                type: 'studio',
                hasVoice: true,
                voiceModels: ['studio-voice-1', 'studio-voice-2', 'studio-voice-arabic']
            },
            claude: {
                name: 'Anthropic Claude',
                baseUrl: 'https://api.anthropic.com/v1',
                models: ['claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-3-opus-20240229', 'claude-3-5-sonnet-20241022'],
                requiresKey: true,
                type: 'messages',
                hasVoice: false
            },
            'perplexity': {
                name: 'Perplexity AI',
                baseUrl: 'https://api.perplexity.ai',
                models: ['llama-3.1-sonar-small-128k-online', 'llama-3.1-sonar-large-128k-online', 'llama-3.1-8b-instruct', 'llama-3.1-70b-instruct'],
                requiresKey: true,
                type: 'chat',
                hasVoice: false
            },
            'groq': {
                name: 'Groq',
                baseUrl: 'https://api.groq.com/openai/v1',
                models: ['llama3-8b-8192', 'llama3-70b-8192', 'mixtral-8x7b-32768', 'gemma-7b-it'],
                requiresKey: true,
                type: 'chat',
                hasVoice: false
            },
            'mistral': {
                name: 'Mistral AI',
                baseUrl: 'https://api.mistral.ai/v1',
                models: ['mistral-tiny', 'mistral-small', 'mistral-medium', 'mistral-large-latest'],
                requiresKey: true,
                type: 'chat',
                hasVoice: false
            },
            'deepseek': {
                name: 'DeepSeek AI',
                baseUrl: 'https://api.deepseek.com/v1',
                models: ['deepseek-chat', 'deepseek-reasoner'],
                requiresKey: true,
                type: 'chat',
                hasVoice: false,
                description: 'DeepSeek-V3 and DeepSeek-R1 reasoning models'
            },
            cohere: {
                name: 'Cohere',
                baseUrl: 'https://api.cohere.ai/v1',
                models: ['command', 'command-light'],
                requiresKey: true,
                type: 'generate'
            },
            huggingface: {
                name: 'Hugging Face',
                baseUrl: 'https://api-inference.huggingface.co/models',
                models: ['microsoft/DialoGPT-large', 'facebook/blenderbot-400M-distill'],
                requiresKey: true,
                type: 'inference'
            },
            custom: {
                name: 'Custom API',
                baseUrl: '',
                models: ['custom-model'],
                requiresKey: false,
                type: 'custom'
            }
        };
    }

    // Load API configurations
    loadAPIConfigs() {
        try {
            return JSON.parse(localStorage.getItem('apiConfigs') || '{}');
        } catch {
            return {};
        }
    }

    // Save API configurations
    saveAPIConfigs() {
        localStorage.setItem('apiConfigs', JSON.stringify(this.apiConfigs));
    }

    // Load settings
    loadSettings() {
        try {
            const settings = JSON.parse(localStorage.getItem('apiManagerSettings') || '{}');
            this.isEnabled = settings.isEnabled || false;
            this.currentProvider = settings.currentProvider || null;
        } catch {
            this.isEnabled = false;
            this.currentProvider = null;
        }
    }

    // Save settings
    saveSettings() {
        const settings = {
            isEnabled: this.isEnabled,
            currentProvider: this.currentProvider
        };
        localStorage.setItem('apiManagerSettings', JSON.stringify(settings));
    }

    // Enable API integration
    enable() {
        this.isEnabled = true;
        this.saveSettings();
        console.log('🔌 تم تفعيل تكامل API');
        this.updateUI();
    }

    // Disable API integration
    disable() {
        this.isEnabled = false;
        this.saveSettings();
        console.log('🔌 تم إيقاف تكامل API');
        this.updateUI();
    }

    // Toggle API integration
    toggle() {
        if (this.isEnabled) {
            this.disable();
        } else {
            this.enable();
        }
        return this.isEnabled;
    }

    // Configure API provider
    configureProvider(provider, config) {
        if (!this.supportedProviders[provider]) {
            throw new Error(`مزود غير مدعوم: ${provider}`);
        }

        this.apiConfigs[provider] = {
            ...config,
            configuredAt: new Date().toISOString()
        };

        this.saveAPIConfigs();
        console.log(`✅ تم تكوين ${this.supportedProviders[provider].name}`);
    }

    // Check API availability
    async checkAPIAvailability() {
        const availableProviders = [];

        for (const [key, provider] of Object.entries(this.supportedProviders)) {
            if (this.apiConfigs[key] && this.apiConfigs[key].apiKey) {
                try {
                    const isAvailable = await this.testProvider(key);
                    if (isAvailable) {
                        availableProviders.push(key);
                    }
                } catch (error) {
                    console.warn(`⚠️ ${provider.name} غير متاح:`, error.message);
                }
            }
        }

        console.log(`🔌 متاح ${availableProviders.length} من مزودي API`);
        return availableProviders;
    }

    // Test provider connection
    async testProvider(provider) {
        const config = this.apiConfigs[provider];
        const providerInfo = this.supportedProviders[provider];

        if (!config) {
            console.error(`لا توجد إعدادات للمزود ${provider}`);
            return false;
        }

        if (!config.apiKey && provider !== 'custom') {
            console.error(`مفتاح API مفقود للمزود ${provider}`);
            return false;
        }

        try {
            console.log(`🧪 اختبار الاتصال مع ${providerInfo.name}...`);

            // إنشاء طلب اختبار مبسط
            const testMessage = 'مرحبا';
            const requestData = this.buildRequestData(provider, 'test', {
                message: testMessage,
                maxTokens: 50,
                temperature: 0.1
            });

            const requestOptions = this.buildRequestOptions(provider, requestData);

            // إرسال طلب الاختبار
            const response = await fetch(requestData.url, requestOptions);

            console.log(`📡 رد الخادم: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`❌ خطأ HTTP ${response.status}:`, errorText);

                // رسائل خطأ مفصلة
                if (response.status === 401) {
                    throw new Error('مفتاح API غير صحيح أو منتهي الصلاحية');
                } else if (response.status === 403) {
                    throw new Error('ليس لديك صلاحية للوصول لهذا النموذج');
                } else if (response.status === 429) {
                    throw new Error('تم تجاوز حد الطلبات المسموح');
                } else if (response.status === 404) {
                    throw new Error('النموذج أو نقطة النهاية غير موجودة');
                } else {
                    throw new Error(`خطأ في الخادم: ${response.status} - ${errorText}`);
                }
            }

            const data = await response.json();
            console.log('✅ تم الحصول على رد ناجح من الخادم');

            // التحقق من وجود المحتوى المتوقع
            const parsedResponse = this.parseResponse(provider, data);

            if (parsedResponse && parsedResponse.text) {
                console.log(`✅ اختبار ${providerInfo.name} نجح:`, parsedResponse.text.substring(0, 50));
                return true;
            } else {
                console.error('❌ الرد لا يحتوي على نص صالح');
                return false;
            }

        } catch (error) {
            console.error(`❌ خطأ في اختبار ${providerInfo.name}:`, error.message);
            throw error; // إعادة رمي الخطأ ليتم عرضه في الواجهة
        }
    }

    // Make API request
    async makeAPIRequest(provider, type, params) {
        if (!this.isEnabled) {
            throw new Error('تكامل API غير مفعل');
        }

        const config = this.apiConfigs[provider];
        const providerInfo = this.supportedProviders[provider];

        if (!config || !config.apiKey) {
            throw new Error(`${providerInfo.name} غير مكون`);
        }

        const requestData = this.buildRequestData(provider, type, params);
        const requestOptions = this.buildRequestOptions(provider, requestData);

        try {
            console.log(`🔌 إرسال طلب إلى ${providerInfo.name}...`);
            
            const response = await fetch(requestData.url, requestOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            const result = this.parseResponse(provider, data);

            // Save to history
            this.addToHistory(provider, params, result);

            console.log(`✅ تم الحصول على رد من ${providerInfo.name}`);
            return result;

        } catch (error) {
            console.error(`❌ خطأ في ${providerInfo.name}:`, error);
            throw error;
        }
    }

    // Build request data
    buildRequestData(provider, type, params) {
        const config = this.apiConfigs[provider];
        const providerInfo = this.supportedProviders[provider];
        const model = config.model || providerInfo.models[0];

        switch (provider) {
            case 'openai':
                return {
                    url: `${providerInfo.baseUrl}/chat/completions`,
                    body: {
                        model: model,
                        messages: [
                            {
                                role: 'user',
                                content: params.message
                            }
                        ],
                        max_tokens: params.maxTokens || 1000,
                        temperature: params.temperature || 0.7
                    }
                };

            case 'gemini':
                return {
                    url: `${providerInfo.baseUrl}/models/${model}:generateContent?key=${config.apiKey}`,
                    body: {
                        contents: [
                            {
                                parts: [
                                    {
                                        text: params.message
                                    }
                                ]
                            }
                        ],
                        generationConfig: {
                            maxOutputTokens: params.maxTokens || 1000,
                            temperature: params.temperature || 0.7
                        }
                    }
                };

            case 'gemini-studio':
                // Gemini Studio uses the same API as regular Gemini but with different base URL
                return {
                    url: `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${config.apiKey}`,
                    body: {
                        contents: [
                            {
                                parts: [
                                    {
                                        text: params.message
                                    }
                                ]
                            }
                        ],
                        generationConfig: {
                            maxOutputTokens: params.maxTokens || 1000,
                            temperature: params.temperature || 0.7
                        }
                    }
                };

            case 'claude':
                return {
                    url: `${providerInfo.baseUrl}/messages`,
                    body: {
                        model: model,
                        max_tokens: params.maxTokens || 1000,
                        messages: [
                            {
                                role: 'user',
                                content: params.message
                            }
                        ]
                    }
                };

            case 'perplexity':
            case 'groq':
            case 'mistral':
            case 'deepseek':
                return {
                    url: `${providerInfo.baseUrl}/chat/completions`,
                    body: {
                        model: model,
                        messages: [
                            {
                                role: 'user',
                                content: params.message
                            }
                        ],
                        max_tokens: params.maxTokens || 1000,
                        temperature: params.temperature || 0.7
                    }
                };

            case 'custom':
                return {
                    url: config.endpoint || providerInfo.baseUrl,
                    body: {
                        message: params.message,
                        ...params
                    }
                };

            default:
                throw new Error(`مزود غير مدعوم: ${provider}`);
        }
    }

    // Build request options
    buildRequestOptions(provider, requestData) {
        const config = this.apiConfigs[provider];
        const headers = {
            'Content-Type': 'application/json'
        };

        // Add authentication headers
        switch (provider) {
            case 'openai':
                headers['Authorization'] = `Bearer ${config.apiKey}`;
                break;
            case 'claude':
                headers['x-api-key'] = config.apiKey;
                headers['anthropic-version'] = '2023-06-01';
                break;
            case 'cohere':
                headers['Authorization'] = `Bearer ${config.apiKey}`;
                break;
            case 'huggingface':
                headers['Authorization'] = `Bearer ${config.apiKey}`;
                break;
            case 'perplexity':
            case 'groq':
            case 'mistral':
            case 'deepseek':
                headers['Authorization'] = `Bearer ${config.apiKey}`;
                break;
            case 'custom':
                if (config.apiKey) {
                    headers['Authorization'] = `Bearer ${config.apiKey}`;
                }
                break;
        }

        return {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestData.body)
        };
    }

    // Parse response
    parseResponse(provider, data) {
        switch (provider) {
            case 'openai':
                return {
                    text: data.choices[0].message.content,
                    usage: data.usage
                };

            case 'gemini':
            case 'gemini-studio':
                if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                    return {
                        text: data.candidates[0].content.parts[0].text,
                        usage: data.usageMetadata
                    };
                } else if (data.error) {
                    throw new Error(`Gemini API Error: ${data.error.message}`);
                } else {
                    throw new Error('رد غير متوقع من Gemini API');
                }

            case 'claude':
                return {
                    text: data.content[0].text,
                    usage: data.usage
                };

            case 'perplexity':
            case 'groq':
            case 'mistral':
            case 'deepseek':
                return {
                    text: data.choices[0].message.content,
                    usage: data.usage
                };

            case 'custom':
                return {
                    text: data.response || data.text || data.message || 'لا يوجد رد',
                    usage: data.usage || null
                };

            default:
                return {
                    text: data.text || data.response || 'لا يوجد رد',
                    usage: null
                };
        }
    }

    // Add to request history
    addToHistory(provider, request, response) {
        this.requestHistory.unshift({
            provider: provider,
            request: request,
            response: response,
            timestamp: new Date().toISOString()
        });

        // Keep only last 50 requests
        if (this.requestHistory.length > 50) {
            this.requestHistory = this.requestHistory.slice(0, 50);
        }
    }

    // Get available providers
    getAvailableProviders() {
        return Object.keys(this.apiConfigs).filter(provider => 
            this.apiConfigs[provider] && this.apiConfigs[provider].apiKey
        );
    }

    // Set current provider
    setCurrentProvider(provider) {
        if (this.getAvailableProviders().includes(provider)) {
            this.currentProvider = provider;
            this.saveSettings();
            console.log(`🔌 تم تعيين ${this.supportedProviders[provider].name} كمزود حالي`);
        } else {
            throw new Error(`المزود ${provider} غير متاح`);
        }
    }

    // Send message to current provider
    async sendMessage(message, options = {}) {
        if (!this.isEnabled) {
            throw new Error('تكامل API غير مفعل');
        }

        if (!this.currentProvider) {
            throw new Error('لم يتم تعيين مزود API');
        }

        return await this.makeAPIRequest(this.currentProvider, 'chat', {
            message: message,
            ...options
        });
    }

    // Convert text to speech using current provider
    async textToSpeech(text, options = {}) {
        if (!this.isEnabled) {
            throw new Error('تكامل API غير مفعل');
        }

        if (!this.currentProvider) {
            throw new Error('لم يتم تعيين مزود API');
        }

        const providerInfo = this.supportedProviders[this.currentProvider];

        if (!providerInfo.hasVoice) {
            throw new Error(`${providerInfo.name} لا يدعم تحويل النص إلى كلام`);
        }

        return await this.makeVoiceRequest(this.currentProvider, text, options);
    }

    // Make voice request
    async makeVoiceRequest(provider, text, options = {}) {
        const config = this.apiConfigs[provider];
        const providerInfo = this.supportedProviders[provider];

        if (!config || !config.apiKey) {
            throw new Error(`${providerInfo.name} غير مكون`);
        }

        try {
            console.log(`🔊 طلب تحويل نص إلى كلام من ${providerInfo.name}...`);

            switch (provider) {
                case 'openai':
                    return await this.openAITextToSpeech(text, options);
                case 'gemini':
                case 'gemini-studio':
                    return await this.geminiTextToSpeech(text, options);
                default:
                    throw new Error(`تحويل النص إلى كلام غير مدعوم لـ ${provider}`);
            }
        } catch (error) {
            console.error(`❌ خطأ في تحويل النص إلى كلام من ${providerInfo.name}:`, error);
            throw error;
        }
    }

    // OpenAI Text-to-Speech
    async openAITextToSpeech(text, options = {}) {
        const config = this.apiConfigs['openai'];
        const voice = options.voice || config.voice || 'alloy';
        const model = options.model || 'tts-1';

        const response = await fetch('https://api.openai.com/v1/audio/speech', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: model,
                input: text,
                voice: voice,
                response_format: 'mp3'
            })
        });

        if (!response.ok) {
            throw new Error(`OpenAI TTS error: ${response.status}`);
        }

        const audioBlob = await response.blob();
        return {
            audio: audioBlob,
            format: 'mp3',
            provider: 'OpenAI',
            voice: voice
        };
    }

    // Gemini Text-to-Speech (simulated - Google doesn't have direct TTS API in Gemini)
    async geminiTextToSpeech(text, options = {}) {
        // For now, we'll use Web Speech API with a different voice
        // In the future, this could integrate with Google Cloud TTS

        const voice = options.voice || 'ar-SA'; // Arabic voice

        return {
            text: text,
            voice: voice,
            provider: 'Gemini',
            useWebSpeech: true,
            webSpeechOptions: {
                lang: 'ar-SA',
                rate: 0.9,
                pitch: 1.1
            }
        };
    }

    // Play audio from API response
    async playAPIAudio(audioResponse) {
        try {
            if (audioResponse.useWebSpeech) {
                // Use Web Speech API with custom settings
                if (window.speechSynthesis && typeof window.speakText === 'function') {
                    const utterance = new SpeechSynthesisUtterance(audioResponse.text);

                    if (audioResponse.webSpeechOptions) {
                        utterance.lang = audioResponse.webSpeechOptions.lang;
                        utterance.rate = audioResponse.webSpeechOptions.rate;
                        utterance.pitch = audioResponse.webSpeechOptions.pitch;
                    }

                    // Try to find Arabic voice
                    const voices = speechSynthesis.getVoices();
                    const arabicVoice = voices.find(voice =>
                        voice.lang.includes('ar') || voice.name.includes('Arabic')
                    );

                    if (arabicVoice) {
                        utterance.voice = arabicVoice;
                    }

                    speechSynthesis.speak(utterance);
                    console.log(`🔊 تم تشغيل الصوت من ${audioResponse.provider}`);
                }
            } else if (audioResponse.audio) {
                // Play audio blob
                const audioUrl = URL.createObjectURL(audioResponse.audio);
                const audio = new Audio(audioUrl);

                audio.onended = () => {
                    URL.revokeObjectURL(audioUrl);
                };

                await audio.play();
                console.log(`🔊 تم تشغيل الصوت من ${audioResponse.provider}`);
            }
        } catch (error) {
            console.error('خطأ في تشغيل الصوت:', error);
            // Fallback to default speech
            if (typeof window.speakText === 'function') {
                window.speakText(audioResponse.text || 'حدث خطأ في تشغيل الصوت');
            }
        }
    }

    // Update UI elements
    updateUI() {
        // Update status indicators
        const statusElements = document.querySelectorAll('.api-status');
        statusElements.forEach(element => {
            element.textContent = this.isEnabled ? 'مفعل' : 'غير مفعل';
            element.className = `api-status ${this.isEnabled ? 'enabled' : 'disabled'}`;
        });

        // Update provider info
        const providerElements = document.querySelectorAll('.current-provider');
        providerElements.forEach(element => {
            if (this.currentProvider) {
                element.textContent = this.supportedProviders[this.currentProvider].name;
            } else {
                element.textContent = 'غير محدد';
            }
        });
    }

    // Show configuration interface
    showConfigInterface() {
        if (window.apiConfigInterface) {
            window.apiConfigInterface.show();
        } else {
            console.log('واجهة تكوين API غير متاحة');
        }
    }

    // Integration with assistant features
    async processWithFeatures(message, features = {}) {
        if (!this.isEnabled || !this.currentProvider) {
            return null;
        }

        try {
            let enhancedMessage = message;
            let context = '';

            // Screen sharing integration
            if (features.screenSharing && window.screenCapture) {
                context += '\n[المستخدم يشارك الشاشة حالياً]';
                enhancedMessage += '\n\nملاحظة: المستخدم يشارك شاشته معي.';
            }

            // File generation context
            if (features.fileGeneration) {
                context += '\n[يمكنني إنشاء وتحميل الملفات]';
                enhancedMessage += '\n\nملاحظة: يمكنني إنشاء ملفات وتحميلها للمستخدم.';
            }

            // Voice context
            if (features.voiceInput) {
                context += '\n[تم استقبال الرسالة صوتياً]';
                enhancedMessage += '\n\nملاحظة: المستخدم تحدث معي صوتياً.';
            }

            // 3D visualization context
            if (features.threeDVisualization && window.threeDViewer) {
                context += '\n[يمكنني عرض نماذج ثلاثية الأبعاد]';
                enhancedMessage += '\n\nملاحظة: يمكنني عرض نماذج ثلاثية الأبعاد.';
            }

            // Video analysis context
            if (features.videoAnalysis && window.videoAnalyzer) {
                context += '\n[يمكنني تحليل الفيديوهات]';
                enhancedMessage += '\n\nملاحظة: يمكنني تحليل الفيديوهات.';
            }

            // Translation context
            if (features.translation && window.translator) {
                context += '\n[يمكنني الترجمة بين اللغات]';
                enhancedMessage += '\n\nملاحظة: يمكنني الترجمة بين اللغات المختلفة.';
            }

            // Send enhanced message
            const response = await this.sendMessage(enhancedMessage, {
                maxTokens: 1500,
                temperature: 0.7,
                context: context
            });

            return response;

        } catch (error) {
            console.error('خطأ في معالجة الرسالة مع الميزات:', error);
            return null;
        }
    }

    // Generate file with API assistance
    async generateFileWithAPI(fileType, description, content = '') {
        if (!this.isEnabled || !this.currentProvider) {
            return null;
        }

        try {
            const prompt = `أريد إنشاء ملف من نوع ${fileType}.
الوصف: ${description}
${content ? `المحتوى الحالي: ${content}` : ''}

يرجى إنشاء محتوى الملف المناسب. قدم المحتوى فقط بدون شرح إضافي.`;

            const response = await this.sendMessage(prompt, {
                maxTokens: 2000,
                temperature: 0.3
            });

            if (response && response.text) {
                return {
                    content: response.text,
                    type: fileType,
                    provider: this.supportedProviders[this.currentProvider].name
                };
            }

            return null;

        } catch (error) {
            console.error('خطأ في إنشاء الملف:', error);
            return null;
        }
    }

    // Analyze screen content with API
    async analyzeScreenWithAPI(imageData) {
        if (!this.isEnabled || !this.currentProvider) {
            return null;
        }

        // Only works with vision-capable models
        const providerInfo = this.supportedProviders[this.currentProvider];
        const visionModels = ['gemini-pro-vision', 'gpt-4-vision-preview', 'gpt-4o'];

        const config = this.apiConfigs[this.currentProvider];
        const currentModel = config.model || providerInfo.models[0];

        if (!visionModels.includes(currentModel)) {
            console.warn('النموذج الحالي لا يدعم تحليل الصور');
            return null;
        }

        try {
            const prompt = 'حلل هذه الصورة من الشاشة واشرح ما تراه. قدم وصفاً مفصلاً ومفيداً.';

            // This would need to be implemented based on the specific API
            // For now, return a placeholder
            return {
                analysis: 'تحليل الشاشة متاح مع النماذج المدعومة للرؤية',
                provider: providerInfo.name,
                model: currentModel
            };

        } catch (error) {
            console.error('خطأ في تحليل الشاشة:', error);
            return null;
        }
    }

    // Get available features for current provider
    getProviderFeatures() {
        if (!this.currentProvider) {
            return {};
        }

        const providerInfo = this.supportedProviders[this.currentProvider];
        const config = this.apiConfigs[this.currentProvider];
        const currentModel = config?.model || providerInfo.models[0];

        return {
            textGeneration: true,
            voiceOutput: providerInfo.hasVoice || false,
            visionAnalysis: this.hasVisionSupport(currentModel),
            codeGeneration: true,
            fileGeneration: true,
            translation: true,
            reasoning: this.hasReasoningSupport(currentModel),
            longContext: this.hasLongContextSupport(currentModel)
        };
    }

    // Check if model supports vision
    hasVisionSupport(model) {
        const visionModels = [
            'gemini-pro-vision', 'gpt-4-vision-preview', 'gpt-4o',
            'claude-3-opus-20240229', 'claude-3-sonnet-20240229'
        ];
        return visionModels.includes(model);
    }

    // Check if model supports advanced reasoning
    hasReasoningSupport(model) {
        const reasoningModels = [
            'gpt-4', 'gpt-4-turbo', 'gpt-4o', 'claude-3-opus-20240229',
            'claude-3-5-sonnet-20241022', 'gemini-1.5-pro', 'deepseek-chat', 'deepseek-reasoner'
        ];
        return reasoningModels.includes(model);
    }

    // Check if model supports long context
    hasLongContextSupport(model) {
        const longContextModels = [
            'gemini-1.5-pro', 'claude-3-5-sonnet-20241022', 'gpt-4-turbo',
            'llama-3.1-sonar-large-128k-online', 'deepseek-chat', 'deepseek-reasoner'
        ];
        return longContextModels.includes(model);
    }
}

// Create global instance
const apiManager = new APIManager();

// Export for global use
if (typeof window !== 'undefined') {
    window.apiManager = apiManager;
    window.APIManager = APIManager;
}
