/**
 * AI Self-Improvement System - Main Controller
 * Orchestrates the entire self-improvement process
 */

class AISelfImprove {
    constructor() {
        this.isActive = false;
        this.isScanning = false;
        this.currentOpportunities = [];
        this.availableAgents = {};
        this.scanInterval = null;
        this.settings = this.loadSettings();

        this.init();
    }

    // Initialize the system
    async init() {
        console.log('🤖 تهيئة نظام التحسين الذاتي...');
        
        // Check available AI agents
        await this.checkAvailableAgents();
        
        // Set up automatic scanning if enabled
        if (this.settings.autoScan) {
            this.startAutoScan();
        }
        
        console.log('✅ تم تهيئة نظام التحسين الذاتي');
    }

    // Check available AI agents (flexible system)
    async checkAvailableAgents() {
        // Load custom AI agents from settings
        const customAgents = this.settings.customAgents || {};

        this.availableAgents = {
            // Built-in agents
            'augment_ai': true, // Always available in VS Code
            'lm_studio': await this.checkLMStudio(),
            'openai_gpt4': this.checkOpenAI(),
            'claude_ai': this.checkClaude(),
            'gemini_ai': this.checkGemini(),
            'custom_endpoint': this.checkCustomEndpoint(),

            // User-defined custom agents
            ...customAgents
        };

        const availableCount = Object.values(this.availableAgents).filter(Boolean).length;
        console.log(`🧠 متاح ${availableCount} من أنظمة الذكاء الاصطناعي`);

        // Update UI if available
        if (document.getElementById('availableAI')) {
            document.getElementById('availableAI').textContent = `${availableCount} نظام متاح`;
        }
    }

    // Check LM Studio availability
    async checkLMStudio() {
        try {
            const response = await fetch('http://127.0.0.1:1234/v1/models', {
                method: 'GET',
                timeout: 5000
            });
            return response.ok;
        } catch {
            return false;
        }
    }

    // Check OpenAI availability (requires API key)
    checkOpenAI() {
        return localStorage.getItem('openai_api_key') !== null;
    }

    // Check Claude AI availability
    checkClaude() {
        return localStorage.getItem('claude_api_key') !== null;
    }

    // Check Gemini AI availability
    checkGemini() {
        return localStorage.getItem('gemini_api_key') !== null;
    }

    // Check custom endpoint availability
    checkCustomEndpoint() {
        const customEndpoint = localStorage.getItem('custom_ai_endpoint');
        return customEndpoint !== null && customEndpoint.trim() !== '';
    }

    // Add custom AI agent
    addCustomAgent(name, config) {
        if (!this.settings.customAgents) {
            this.settings.customAgents = {};
        }

        this.settings.customAgents[name] = {
            name: config.displayName || name,
            endpoint: config.endpoint,
            apiKey: config.apiKey,
            type: config.type || 'custom',
            model: config.model || 'default',
            available: true,
            addedBy: 'user',
            timestamp: new Date().toISOString()
        };

        this.saveSettings();
        this.availableAgents[name] = true;

        console.log(`✅ تم إضافة نظام ذكاء جديد: ${config.displayName || name}`);

        // Update UI
        this.updateAvailableAgentsUI();

        return true;
    }

    // Remove custom AI agent
    removeCustomAgent(name) {
        if (this.settings.customAgents && this.settings.customAgents[name]) {
            delete this.settings.customAgents[name];
            delete this.availableAgents[name];
            this.saveSettings();

            console.log(`🗑️ تم حذف نظام الذكاء: ${name}`);
            this.updateAvailableAgentsUI();
            return true;
        }
        return false;
    }

    // Update available agents UI
    updateAvailableAgentsUI() {
        const availableCount = Object.values(this.availableAgents).filter(Boolean).length;
        if (document.getElementById('availableAI')) {
            document.getElementById('availableAI').textContent = `${availableCount} نظام متاح`;
        }
    }

    // Start automatic scanning
    startAutoScan() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }

        // Scan every 30 minutes
        this.scanInterval = setInterval(() => {
            this.startScan(true); // Silent scan
        }, 30 * 60 * 1000);

        console.log('🔄 تم تفعيل الفحص التلقائي');
    }

    // Stop automatic scanning
    stopAutoScan() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
            this.scanInterval = null;
        }
        console.log('⏹️ تم إيقاف الفحص التلقائي');
    }

    // Start improvement scan
    async startScan(silent = false) {
        if (this.isScanning) {
            if (!silent) {
                this.showMessage('الفحص جاري بالفعل...', 'info');
            }
            return;
        }

        this.isScanning = true;
        
        if (!silent) {
            this.showMessage('🔍 بدء فحص الكود للبحث عن فرص التحسين...', 'info');
        }

        try {
            // Call Python backend for scanning
            const opportunities = await this.scanForOpportunities();
            this.currentOpportunities = opportunities;

            if (!silent) {
                if (opportunities.length > 0) {
                    this.showMessage(`تم اكتشاف ${opportunities.length} فرصة للتحسين`, 'success');
                    suggestionPresenter.show(opportunities);
                } else {
                    this.showMessage('ممتاز! لم يتم العثور على مشاكل في الكود', 'success');
                }
            }

            // Auto-request AI improvements for critical issues
            if (this.settings.autoRequestAI) {
                await this.autoRequestCriticalImprovements(opportunities);
            }

        } catch (error) {
            console.error('خطأ في فحص الكود:', error);
            if (!silent) {
                this.showMessage('خطأ في فحص الكود', 'error');
            }
        } finally {
            this.isScanning = false;
        }
    }

    // Scan for improvement opportunities
    async scanForOpportunities() {
        // This would call the Python backend
        // For now, return mock data for demonstration
        return [
            {
                id: 'imp_001',
                type: 'performance',
                severity: 'medium',
                description: 'استخدام document.getElementById متكرر يمكن تحسينه بالتخزين المؤقت',
                file_path: 'assets/assistant-core.js',
                line_numbers: [445],
                current_code: "const messageInput = document.getElementById('messageInput');",
                context: "function sendMessage() {\n    const messageInput = document.getElementById('messageInput');\n    const message = messageInput.value.trim();\n}"
            },
            {
                id: 'imp_002',
                type: 'security',
                severity: 'high',
                description: 'استخدام innerHTML مع متغيرات قد يؤدي لثغرات XSS',
                file_path: 'assets/script.js',
                line_numbers: [89],
                current_code: "element.innerHTML = userInput + ' additional content';",
                context: "function updateContent(userInput) {\n    const element = document.getElementById('content');\n    element.innerHTML = userInput + ' additional content';\n}"
            },
            {
                id: 'imp_003',
                type: 'code_quality',
                severity: 'low',
                description: 'استخدام var بدلاً من const/let',
                file_path: 'assets/modules/voice/AdvancedVoiceEngine.js',
                line_numbers: [156],
                current_code: "var voices = speechSynthesis.getVoices();",
                context: "function selectBestArabicVoice() {\n    var voices = speechSynthesis.getVoices();\n    const profile = this.voiceProfiles[this.currentProfile];\n}"
            }
        ];
    }

    // Auto-request AI improvements for critical issues
    async autoRequestCriticalImprovements(opportunities) {
        const criticalOpportunities = opportunities.filter(opp => 
            opp.severity === 'critical' || opp.severity === 'high'
        );

        for (const opportunity of criticalOpportunities) {
            try {
                const aiResponse = await this.requestAIImprovement(opportunity);
                if (aiResponse) {
                    this.showMessage(`تم الحصول على اقتراح تحسين لـ ${opportunity.description}`, 'info');
                }
            } catch (error) {
                console.error('خطأ في طلب التحسين التلقائي:', error);
            }
        }
    }

    // Request AI improvement for specific opportunity (integrated with existing models)
    async requestAIImprovement(opportunity, preferredAgent = null, userDirected = false) {
        // If user-directed, show selection interface
        if (userDirected) {
            return await this.showAgentSelectionInterface(opportunity);
        }

        console.log(`🤖 طلب تحسين للكود...`);

        try {
            // إنشاء prompt للتحسين
            const improvementPrompt = `قم بتحليل وتحسين الكود التالي:

نوع المشكلة: ${opportunity.type}
مستوى الخطورة: ${opportunity.severity}
الوصف: ${opportunity.description}

الكود الحالي:
\`\`\`javascript
${opportunity.current_code}
\`\`\`

السياق:
\`\`\`javascript
${opportunity.context}
\`\`\`

المطلوب:
1. 🔍 تحليل المشكلة بالتفصيل
2. 💡 اقتراح حل محسن ومحدث
3. 📝 شرح سبب التحسين
4. ⚡ تحسين الأداء إن أمكن
5. 🛡️ تحسين الأمان إن لزم الأمر
6. 📚 أفضل الممارسات المطبقة

قدم الكود المحسن مع شرح مفصل:`;

            let aiResponse = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter للتحسين...');
                const response = await window.openRouterIntegration.smartSendMessage(improvementPrompt, {
                    mode: 'code_improvement',
                    temperature: 0.2,
                    maxTokens: 3000
                });
                if (response && response.text) {
                    aiResponse = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!aiResponse && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face للتحسين...');
                const response = await window.huggingFaceManager.sendMessage(improvementPrompt);
                if (response && response.text) {
                    aiResponse = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!aiResponse && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي للتحسين...');
                aiResponse = await technicalAssistant.getResponse(improvementPrompt);
            }

            if (aiResponse) {
                return {
                    success: true,
                    improvement: aiResponse,
                    opportunity: opportunity,
                    timestamp: new Date().toISOString()
                };
            } else {
                throw new Error('لا توجد نماذج ذكاء اصطناعي متاحة للتحسين');
            }

        } catch (error) {
            console.error(`خطأ في طلب التحسين:`, error);
            throw error;
        }
    }

    // Show agent selection interface for user direction
    async showAgentSelectionInterface(opportunity) {
        return new Promise((resolve) => {
            const modal = this.createAgentSelectionModal(opportunity, resolve);
            document.body.appendChild(modal);
        });
    }

    // Create agent selection modal
    createAgentSelectionModal(opportunity, callback) {
        const modal = document.createElement('div');
        modal.className = 'agent-selection-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10001;
            backdrop-filter: blur(10px);
        `;

        const availableAgents = Object.entries(this.availableAgents)
            .filter(([_, available]) => available)
            .map(([key, _]) => {
                const config = this.settings.customAgents?.[key];
                return {
                    key,
                    name: config?.name || this.getAgentDisplayName(key),
                    type: config?.type || this.getAgentType(key)
                };
            });

        modal.innerHTML = `
            <div class="agent-selection-content" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                padding: 30px;
                max-width: 600px;
                width: 90%;
                color: white;
                text-align: center;
            ">
                <h3 style="margin: 0 0 20px 0; font-size: 1.5rem;">
                    <i class="fas fa-robot"></i> اختر نظام الذكاء الاصطناعي
                </h3>
                <p style="margin-bottom: 25px; opacity: 0.9;">
                    أي نظام ذكاء اصطناعي تريد أن أطلب منه المساعدة؟
                </p>
                <div class="agents-grid" style="
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 15px;
                    margin-bottom: 25px;
                ">
                    ${availableAgents.map(agent => `
                        <button class="agent-option" data-agent="${agent.key}" style="
                            background: rgba(255, 255, 255, 0.1);
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            border-radius: 15px;
                            padding: 20px;
                            color: white;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            text-align: center;
                        ">
                            <i class="fas fa-${this.getAgentIcon(agent.key)}" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <div style="font-weight: 600; margin-bottom: 5px;">${agent.name}</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">${agent.type}</div>
                        </button>
                    `).join('')}
                </div>
                <div style="display: flex; gap: 15px; justify-content: center;">
                    <button class="add-custom-btn" style="
                        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
                        border: none;
                        color: white;
                        padding: 12px 20px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: 600;
                    ">
                        <i class="fas fa-plus"></i> إضافة نظام جديد
                    </button>
                    <button class="cancel-btn" style="
                        background: rgba(255, 255, 255, 0.2);
                        border: none;
                        color: white;
                        padding: 12px 20px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: 600;
                    ">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelectorAll('.agent-option').forEach(btn => {
            btn.addEventListener('click', async () => {
                const agentKey = btn.dataset.agent;
                modal.remove();

                try {
                    const result = await this.requestAIImprovement(opportunity, agentKey);
                    callback(result);
                } catch (error) {
                    callback({ error: error.message });
                }
            });

            btn.addEventListener('mouseenter', () => {
                btn.style.background = 'rgba(255, 255, 255, 0.2)';
                btn.style.borderColor = 'rgba(255, 255, 255, 0.6)';
                btn.style.transform = 'translateY(-2px)';
            });

            btn.addEventListener('mouseleave', () => {
                btn.style.background = 'rgba(255, 255, 255, 0.1)';
                btn.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                btn.style.transform = 'translateY(0)';
            });
        });

        modal.querySelector('.add-custom-btn').addEventListener('click', () => {
            modal.remove();
            this.showAddCustomAgentInterface(opportunity, callback);
        });

        modal.querySelector('.cancel-btn').addEventListener('click', () => {
            modal.remove();
            callback(null);
        });

        return modal;
    }

    // Show interface to add custom AI agent
    showAddCustomAgentInterface(opportunity, callback) {
        const modal = document.createElement('div');
        modal.className = 'add-custom-agent-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10002;
            backdrop-filter: blur(10px);
        `;

        modal.innerHTML = `
            <div class="add-custom-content" style="
                background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
                border-radius: 20px;
                padding: 30px;
                max-width: 500px;
                width: 90%;
                color: white;
            ">
                <h3 style="margin: 0 0 25px 0; text-align: center;">
                    <i class="fas fa-plus-circle"></i> إضافة نظام ذكاء اصطناعي جديد
                </h3>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600;">اسم النظام:</label>
                    <input type="text" id="agentName" placeholder="مثال: ChatGPT, Claude, Gemini" style="
                        width: 100%;
                        padding: 12px;
                        border: none;
                        border-radius: 8px;
                        font-size: 1rem;
                        box-sizing: border-box;
                    ">
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600;">رابط API:</label>
                    <input type="url" id="agentEndpoint" placeholder="https://api.example.com/v1/chat" style="
                        width: 100%;
                        padding: 12px;
                        border: none;
                        border-radius: 8px;
                        font-size: 1rem;
                        box-sizing: border-box;
                    ">
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600;">مفتاح API (اختياري):</label>
                    <input type="password" id="agentApiKey" placeholder="sk-..." style="
                        width: 100%;
                        padding: 12px;
                        border: none;
                        border-radius: 8px;
                        font-size: 1rem;
                        box-sizing: border-box;
                    ">
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600;">نوع النظام:</label>
                    <select id="agentType" style="
                        width: 100%;
                        padding: 12px;
                        border: none;
                        border-radius: 8px;
                        font-size: 1rem;
                        box-sizing: border-box;
                    ">
                        <option value="openai_compatible">متوافق مع OpenAI</option>
                        <option value="custom_api">API مخصص</option>
                        <option value="webhook">Webhook</option>
                        <option value="local_model">نموذج محلي</option>
                    </select>
                </div>

                <div class="form-group" style="margin-bottom: 25px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600;">اسم النموذج (اختياري):</label>
                    <input type="text" id="agentModel" placeholder="gpt-4, claude-3, gemini-pro" style="
                        width: 100%;
                        padding: 12px;
                        border: none;
                        border-radius: 8px;
                        font-size: 1rem;
                        box-sizing: border-box;
                    ">
                </div>

                <div style="display: flex; gap: 15px; justify-content: center;">
                    <button class="add-btn" style="
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid white;
                        color: white;
                        padding: 12px 25px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: 600;
                        transition: all 0.3s ease;
                    ">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                    <button class="test-btn" style="
                        background: rgba(255, 255, 255, 0.1);
                        border: 2px solid rgba(255, 255, 255, 0.5);
                        color: white;
                        padding: 12px 25px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: 600;
                        transition: all 0.3s ease;
                    ">
                        <i class="fas fa-flask"></i> اختبار
                    </button>
                    <button class="back-btn" style="
                        background: rgba(255, 255, 255, 0.1);
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        color: white;
                        padding: 12px 25px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: 600;
                        transition: all 0.3s ease;
                    ">
                        <i class="fas fa-arrow-left"></i> رجوع
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.add-btn').addEventListener('click', async () => {
            const name = modal.querySelector('#agentName').value.trim();
            const endpoint = modal.querySelector('#agentEndpoint').value.trim();
            const apiKey = modal.querySelector('#agentApiKey').value.trim();
            const type = modal.querySelector('#agentType').value;
            const model = modal.querySelector('#agentModel').value.trim();

            if (!name || !endpoint) {
                alert('يرجى إدخال اسم النظام ورابط API');
                return;
            }

            const config = {
                displayName: name,
                endpoint: endpoint,
                apiKey: apiKey || null,
                type: type,
                model: model || 'default'
            };

            const agentKey = name.toLowerCase().replace(/\s+/g, '_');

            if (this.addCustomAgent(agentKey, config)) {
                modal.remove();

                // Show success message
                this.showMessage(`تم إضافة ${name} بنجاح! جاري طلب التحسين...`, 'success');

                // Request improvement from new agent
                try {
                    const result = await this.requestAIImprovement(opportunity, agentKey);
                    callback(result);
                } catch (error) {
                    callback({ error: error.message });
                }
            }
        });

        modal.querySelector('.test-btn').addEventListener('click', async () => {
            const endpoint = modal.querySelector('#agentEndpoint').value.trim();
            const apiKey = modal.querySelector('#agentApiKey').value.trim();

            if (!endpoint) {
                alert('يرجى إدخال رابط API للاختبار');
                return;
            }

            this.showMessage('جاري اختبار الاتصال...', 'info');

            try {
                const testResult = await this.testCustomEndpoint(endpoint, apiKey);
                if (testResult) {
                    this.showMessage('✅ الاتصال ناجح!', 'success');
                } else {
                    this.showMessage('❌ فشل الاتصال', 'error');
                }
            } catch (error) {
                this.showMessage(`❌ خطأ في الاختبار: ${error.message}`, 'error');
            }
        });

        modal.querySelector('.back-btn').addEventListener('click', () => {
            modal.remove();
            this.showAgentSelectionInterface(opportunity).then(callback);
        });

        document.body.appendChild(modal);
    }

    // Get preferred AI agent (flexible priority)
    getPreferredAgent() {
        // User-defined priority from settings
        const userPriority = this.settings.agentPriority || [];

        // Default priority order
        const defaultPriority = ['augment_ai', 'lm_studio', 'openai_gpt4', 'claude_ai', 'gemini_ai'];

        // Combine user priority with defaults
        const priority = [...userPriority, ...defaultPriority];

        for (const agent of priority) {
            if (this.availableAgents[agent]) {
                return agent;
            }
        }

        // Check custom agents
        const customAgents = Object.keys(this.settings.customAgents || {});
        for (const agent of customAgents) {
            if (this.availableAgents[agent]) {
                return agent;
            }
        }

        return null;
    }

    // Get agent display name
    getAgentDisplayName(key) {
        const names = {
            'augment_ai': 'Augment AI',
            'lm_studio': 'LM Studio',
            'openai_gpt4': 'OpenAI GPT-4',
            'claude_ai': 'Claude AI',
            'gemini_ai': 'Google Gemini',
            'custom_endpoint': 'نقطة نهاية مخصصة'
        };
        return names[key] || key;
    }

    // Get agent type
    getAgentType(key) {
        const types = {
            'augment_ai': 'VS Code Extension',
            'lm_studio': 'نموذج محلي',
            'openai_gpt4': 'سحابي',
            'claude_ai': 'سحابي',
            'gemini_ai': 'سحابي',
            'custom_endpoint': 'مخصص'
        };
        return types[key] || 'مخصص';
    }

    // Get agent icon
    getAgentIcon(key) {
        const icons = {
            'augment_ai': 'code',
            'lm_studio': 'desktop',
            'openai_gpt4': 'cloud',
            'claude_ai': 'brain',
            'gemini_ai': 'star',
            'custom_endpoint': 'plug'
        };
        return icons[key] || 'robot';
    }

    // Test custom endpoint
    async testCustomEndpoint(endpoint, apiKey = null) {
        try {
            const headers = {
                'Content-Type': 'application/json'
            };

            if (apiKey) {
                headers['Authorization'] = `Bearer ${apiKey}`;
            }

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    model: 'test',
                    messages: [{ role: 'user', content: 'test' }],
                    max_tokens: 10
                })
            });

            return response.status < 500; // Accept any non-server error
        } catch (error) {
            console.error('Test endpoint error:', error);
            return false;
        }
    }

    // Request improvement from LM Studio
    async requestFromLMStudio(opportunity) {
        const prompt = this.createImprovementPrompt(opportunity);
        
        const response = await fetch('http://127.0.0.1:1234/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: 'أنت مبرمج خبير متخصص في JavaScript وتطوير الويب. مهمتك مساعدة زملائك في تحسين الكود.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 1500
            })
        });

        if (!response.ok) {
            throw new Error(`LM Studio API error: ${response.status}`);
        }

        const data = await response.json();
        return this.parseAIResponse(data.choices[0].message.content, 'LM Studio');
    }

    // Request improvement from Augment AI
    async requestFromAugmentAI(opportunity) {
        // This would interface with Augment AI through VS Code extension
        const prompt = this.createImprovementPrompt(opportunity);
        
        // For now, return a structured response
        // In real implementation, this would call the VS Code extension API
        return {
            agent_name: 'Augment AI',
            suggested_code: this.generateMockImprovement(opportunity),
            explanation: 'تحسين مقترح من Augment AI بناءً على أفضل الممارسات',
            confidence: 0.95,
            timestamp: new Date().toISOString()
        };
    }

    // Request improvement from OpenAI
    async requestFromOpenAI(opportunity) {
        const apiKey = localStorage.getItem('openai_api_key');
        if (!apiKey) {
            throw new Error('مفتاح OpenAI API غير متوفر');
        }

        const prompt = this.createImprovementPrompt(opportunity);
        
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: 'gpt-4',
                messages: [
                    {
                        role: 'system',
                        content: 'أنت مبرمج خبير متخصص في JavaScript وتطوير الويب. مهمتك مساعدة زملائك في تحسين الكود.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 1500
            })
        });

        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.status}`);
        }

        const data = await response.json();
        return this.parseAIResponse(data.choices[0].message.content, 'GPT-4');
    }

    // Request improvement from Claude AI
    async requestFromClaude(opportunity) {
        const apiKey = localStorage.getItem('claude_api_key');
        if (!apiKey) {
            throw new Error('مفتاح Claude API غير متوفر');
        }

        const prompt = this.createImprovementPrompt(opportunity);

        const response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': apiKey,
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: 'claude-3-sonnet-20240229',
                max_tokens: 1500,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ]
            })
        });

        if (!response.ok) {
            throw new Error(`Claude API error: ${response.status}`);
        }

        const data = await response.json();
        return this.parseAIResponse(data.content[0].text, 'Claude AI');
    }

    // Request improvement from Gemini AI
    async requestFromGemini(opportunity) {
        const apiKey = localStorage.getItem('gemini_api_key');
        if (!apiKey) {
            throw new Error('مفتاح Gemini API غير متوفر');
        }

        const prompt = this.createImprovementPrompt(opportunity);

        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [
                    {
                        parts: [
                            {
                                text: prompt
                            }
                        ]
                    }
                ]
            })
        });

        if (!response.ok) {
            throw new Error(`Gemini API error: ${response.status}`);
        }

        const data = await response.json();
        return this.parseAIResponse(data.candidates[0].content.parts[0].text, 'Google Gemini');
    }

    // Request improvement from custom endpoint
    async requestFromCustomEndpoint(opportunity) {
        const endpoint = localStorage.getItem('custom_ai_endpoint');
        const apiKey = localStorage.getItem('custom_ai_api_key');

        if (!endpoint) {
            throw new Error('نقطة النهاية المخصصة غير متوفرة');
        }

        const prompt = this.createImprovementPrompt(opportunity);

        const headers = {
            'Content-Type': 'application/json'
        };

        if (apiKey) {
            headers['Authorization'] = `Bearer ${apiKey}`;
        }

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({
                model: 'default',
                messages: [
                    {
                        role: 'system',
                        content: 'أنت مبرمج خبير متخصص في JavaScript وتطوير الويب.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 1500
            })
        });

        if (!response.ok) {
            throw new Error(`Custom endpoint error: ${response.status}`);
        }

        const data = await response.json();
        const content = data.choices?.[0]?.message?.content || data.response || data.text || 'لا يوجد رد';

        return this.parseAIResponse(content, 'نقطة نهاية مخصصة');
    }

    // Request improvement from custom agent
    async requestFromCustomAgent(opportunity, agentKey) {
        const agentConfig = this.settings.customAgents[agentKey];

        if (!agentConfig) {
            throw new Error(`إعدادات النظام ${agentKey} غير موجودة`);
        }

        const prompt = this.createImprovementPrompt(opportunity);

        const headers = {
            'Content-Type': 'application/json'
        };

        if (agentConfig.apiKey) {
            headers['Authorization'] = `Bearer ${agentConfig.apiKey}`;
        }

        const body = {
            messages: [
                {
                    role: 'system',
                    content: 'أنت مبرمج خبير متخصص في JavaScript وتطوير الويب.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.3,
            max_tokens: 1500
        };

        if (agentConfig.model && agentConfig.model !== 'default') {
            body.model = agentConfig.model;
        }

        const response = await fetch(agentConfig.endpoint, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(body)
        });

        if (!response.ok) {
            throw new Error(`${agentConfig.name} API error: ${response.status}`);
        }

        const data = await response.json();

        // Try different response formats
        let content = '';
        if (data.choices && data.choices[0] && data.choices[0].message) {
            content = data.choices[0].message.content;
        } else if (data.response) {
            content = data.response;
        } else if (data.text) {
            content = data.text;
        } else if (data.content) {
            content = data.content;
        } else {
            content = 'لا يوجد رد من النظام';
        }

        return this.parseAIResponse(content, agentConfig.name);
    }

    // Create improvement prompt
    createImprovementPrompt(opportunity) {
        return `
مرحباً زميلي المبرمج! أحتاج مساعدتك في تحسين كود JavaScript.

**المشكلة المكتشفة:**
- النوع: ${opportunity.type}
- الخطورة: ${opportunity.severity}
- الوصف: ${opportunity.description}

**الملف:** ${opportunity.file_path}
**السطر:** ${opportunity.line_numbers.join(', ')}

**الكود الحالي:**
\`\`\`javascript
${opportunity.current_code}
\`\`\`

**السياق:**
\`\`\`javascript
${opportunity.context}
\`\`\`

**المطلوب منك:**
1. تحليل المشكلة وتأكيد وجودها
2. اقتراح حل محسن ومحدث
3. كتابة الكود المحسن
4. شرح الفوائد من التحسين

**تنسيق الرد:**
يرجى الرد بتنسيق JSON كالتالي:
\`\`\`json
{
    "analysis": "تحليل المشكلة",
    "improved_code": "الكود المحسن",
    "explanation": "شرح التحسينات",
    "benefits": ["فائدة 1", "فائدة 2"],
    "confidence": 0.95
}
\`\`\`

شكراً لك!
        `;
    }

    // Parse AI response
    parseAIResponse(content, agentName) {
        try {
            // Try to extract JSON from response
            const jsonMatch = content.match(/```json\s*(\{.*?\})\s*```/s);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[1]);
                return {
                    agent_name: agentName,
                    suggested_code: parsed.improved_code || '',
                    explanation: parsed.explanation || '',
                    confidence: parsed.confidence || 0.8,
                    timestamp: new Date().toISOString()
                };
            }
        } catch (error) {
            console.error('خطأ في تحليل رد الذكاء الاصطناعي:', error);
        }

        // Fallback to text response
        return {
            agent_name: agentName,
            suggested_code: '',
            explanation: content,
            confidence: 0.7,
            timestamp: new Date().toISOString()
        };
    }

    // Generate mock improvement (for demonstration)
    generateMockImprovement(opportunity) {
        switch (opportunity.type) {
            case 'performance':
                return opportunity.current_code.replace(
                    "document.getElementById('messageInput')",
                    "this.messageInputCache || (this.messageInputCache = document.getElementById('messageInput'))"
                );
            case 'security':
                return opportunity.current_code.replace(
                    'innerHTML',
                    'textContent'
                );
            case 'code_quality':
                return opportunity.current_code.replace('var ', 'const ');
            default:
                return opportunity.current_code + ' // محسن';
        }
    }

    // Show improvement interface
    showInterface() {
        suggestionPresenter.show(this.currentOpportunities);
    }

    // Load settings
    loadSettings() {
        try {
            return JSON.parse(localStorage.getItem('aiSelfImproveSettings') || '{}');
        } catch {
            return {};
        }
    }

    // Save settings
    saveSettings() {
        localStorage.setItem('aiSelfImproveSettings', JSON.stringify(this.settings));
    }

    // Show message
    showMessage(text, type = 'info') {
        console.log(`${type.toUpperCase()}: ${text}`);
        
        // Also show in UI if available
        if (typeof addMessage === 'function') {
            addMessage('system', `🤖 ${text}`);
        }
    }

    // Activate/deactivate system
    activate() {
        this.isActive = true;
        this.startAutoScan();
        this.showMessage('تم تفعيل نظام التحسين الذاتي', 'success');
    }

    deactivate() {
        this.isActive = false;
        this.stopAutoScan();
        this.showMessage('تم إيقاف نظام التحسين الذاتي', 'info');
    }
}

// Create global instance
const aiSelfImprove = new AISelfImprove();

// Export for global use
if (typeof window !== 'undefined') {
    window.aiSelfImprove = aiSelfImprove;
    window.AISelfImprove = AISelfImprove;
}
